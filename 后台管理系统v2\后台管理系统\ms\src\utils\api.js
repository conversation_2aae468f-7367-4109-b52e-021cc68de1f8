import axios from 'axios'
import router from '../router'
import { ElMessage } from 'element-plus'

// 配置axios默认值
const api = axios.create({
  baseURL: 'http://127.0.0.1:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证头
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理常见错误
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    const { response } = error
    if (response) {
      switch (response.status) {
        case 401:
          localStorage.removeItem('token')
          localStorage.removeItem('userId')
          localStorage.removeItem('userRole')
          localStorage.removeItem('userInfo')
          
          if (router.currentRoute.value.path !== '/login') {
            router.push('/login')
            ElMessage({
              message: '登录已过期，请重新登录',
              type: 'error',
              duration: 3000
            })
          }
          break
          
        case 403:
          ElMessage({
            message: '您没有权限执行此操作',
            type: 'error',
            duration: 3000
          })
          break

        case 404:
          if (response.config.url.includes('/api/')) {
            ElMessage({
              message: '请求的资源不存在',
              type: 'error',
              duration: 3000
            })
          }
          break
          
        default:
          ElMessage({
            message: response.data.message || '请求失败',
            type: 'error',
            duration: 3000
          })
      }
    } else {
      ElMessage({
        message: '网络错误，请检查您的网络连接',
        type: 'error',
        duration: 3000
      })
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const authAPI = {
  // 用户登录
  login: (data) => api.post('/api/auth/login', data),
  // 用户注册
  register: (data) => api.post('/api/auth/register', data),
  // 忘记密码
  forgotPassword: (data) => api.post('/api/auth/forgot-password', data),
  // 重置密码
  resetPassword: (data) => api.post('/api/auth/reset-password', data),
  // 获取当前用户信息
  getCurrentUser: () => api.get('/api/auth/me'),
  // 修改密码
  changePassword: (data) => api.post('/api/auth/change-password', data)
}

export const categoryAPI = {
  // 获取所有分类
  getAll: (params) => api.get('/api/categories', { params }),
  // 获取单个分类
  getById: (id) => api.get(`/api/categories/${id}`),
  // 创建分类
  create: (data) => api.post('/api/categories', data),
  // 更新分类
  update: (id, data) => api.put(`/api/categories/${id}`, data),
  // 删除分类
  delete: (id) => api.delete(`/api/categories/${id}`)
}

export const itemAPI = {
  // 获取所有子项
  getAll: (params) => api.get('/api/items', { params }),
  // 获取默认配置项
  getDefaults: () => api.get('/api/items/defaults'),
  // 根据分类ID获取子项
  getByCategory: (categoryId) => api.get(`/api/items/category/${categoryId}`),
  // 获取单个子项
  getById: (id) => api.get(`/api/items/${id}`),
  // 创建子项
  create: (data) => api.post('/api/items', data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  // 更新子项
  update: (id, data) => api.put(`/api/items/${id}`, data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  // 删除子项
  delete: (id) => api.delete(`/api/items/${id}`),
  // 设置/取消默认状态
  toggleDefault: (id, isDefault) => api.patch(`/api/items/${id}/default`, { is_default: isDefault })
}

export const orderAPI = {
  // 获取所有订单
  getAll: (params) => api.get('/api/orders', { params }),
  // 根据订单号获取订单
  getByNumber: (orderNumber) => api.get(`/api/orders/number/${orderNumber}`),
  // 获取单个订单
  getById: (id) => api.get(`/api/orders/${id}`),
  // 创建订单
  create: (data) => api.post('/api/orders', data),
  // 更新订单
  update: (id, data) => api.put(`/api/orders/${id}`, data),
  // 更新订单状态
  updateStatus: (id, data) => api.put(`/api/orders/${id}/status`, data),
  // 删除订单
  delete: (id) => api.delete(`/api/orders/${id}`)
}

export const userAPI = {
  // 用户登录（对应 router.post('/login', userController.login)）
  login: (data) => api.post('/api/users/login', data),

  // 获取所有用户（对应 router.get('/', userController.getAllUsers)）
  getAll: (params) => api.get('/api/users', { params }),

  // 获取单个用户信息（对应 router.get('/:id', userController.getUserById)）
  getById: (id) => api.get(`/api/users/${id}`),

  // 创建用户（对应 router.post('/', userController.createUser)）
  create: (data) => api.post('/api/users', data),

  // 更新用户信息（对应 router.put('/:id', userController.updateUser)）
  update: (id, data) => api.put(`/api/users/${id}`, data),

  // 重置用户密码
  resetPassword: (id, data) => api.put(`/api/users/${id}/password`, data),

  // 删除用户（对应 router.delete('/:id', userController.deleteUser)）
  delete: (id) => api.delete(`/api/users/${id}`)
}

export default api
