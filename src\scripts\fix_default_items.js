const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'lego_minifigure'
};

async function fixDefaultItems() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 1. 检查当前默认项状态
    console.log('\n=== 检查当前默认项状态 ===');
    const [defaultItems] = await connection.execute(`
      SELECT i.id, i.name, c.main_category, i.is_default 
      FROM items i 
      LEFT JOIN categories c ON i.category_id = c.id 
      WHERE i.is_default = true 
      ORDER BY c.main_category, i.id
    `);
    
    console.log('当前默认项:');
    defaultItems.forEach(item => {
      console.log(`- ${item.main_category}: ${item.name} (ID: ${item.id})`);
    });
    
    // 2. 按大类分组，检查是否有多个默认项
    const categoryGroups = {};
    defaultItems.forEach(item => {
      if (!categoryGroups[item.main_category]) {
        categoryGroups[item.main_category] = [];
      }
      categoryGroups[item.main_category].push(item);
    });
    
    console.log('\n=== 检查重复默认项 ===');
    let hasMultipleDefaults = false;
    
    for (const [category, items] of Object.entries(categoryGroups)) {
      if (items.length > 1) {
        hasMultipleDefaults = true;
        console.log(`❌ ${category} 有 ${items.length} 个默认项:`);
        items.forEach(item => {
          console.log(`   - ${item.name} (ID: ${item.id})`);
        });
        
        // 保留第一个，取消其他的默认状态
        console.log(`   保留第一个: ${items[0].name} (ID: ${items[0].id})`);
        for (let i = 1; i < items.length; i++) {
          await connection.execute(
            'UPDATE items SET is_default = false WHERE id = ?',
            [items[i].id]
          );
          console.log(`   ✅ 已取消 ${items[i].name} (ID: ${items[i].id}) 的默认状态`);
        }
      } else {
        console.log(`✅ ${category} 只有 1 个默认项: ${items[0].name}`);
      }
    }
    
    if (!hasMultipleDefaults) {
      console.log('✅ 没有发现重复的默认项');
    }
    
    // 3. 最终检查
    console.log('\n=== 修复后的默认项状态 ===');
    const [finalDefaultItems] = await connection.execute(`
      SELECT i.id, i.name, c.main_category, i.is_default 
      FROM items i 
      LEFT JOIN categories c ON i.category_id = c.id 
      WHERE i.is_default = true 
      ORDER BY c.main_category, i.id
    `);
    
    console.log('最终默认项:');
    finalDefaultItems.forEach(item => {
      console.log(`- ${item.main_category}: ${item.name} (ID: ${item.id})`);
    });
    
    console.log('\n✅ 默认项修复完成！');
    
  } catch (error) {
    console.error('修复默认项时出错:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行修复脚本
fixDefaultItems();
