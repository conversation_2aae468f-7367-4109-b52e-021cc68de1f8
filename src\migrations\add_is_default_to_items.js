const { pool } = require('../config/db');

// 添加is_default字段到items表
const addIsDefaultField = async () => {
  try {
    const connection = await pool.getConnection();
    
    // 检查字段是否已存在
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'items' 
      AND COLUMN_NAME = 'is_default'
    `);
    
    if (columns.length === 0) {
      // 添加is_default字段
      await connection.query(`
        ALTER TABLE items 
        ADD COLUMN is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认项'
      `);
      console.log('✅ 成功添加is_default字段到items表');
    } else {
      console.log('ℹ️  is_default字段已存在，跳过添加');
    }
    
    connection.release();
  } catch (error) {
    console.error('❌ 添加is_default字段失败:', error);
    throw error;
  }
};

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  addIsDefaultField()
    .then(() => {
      console.log('✅ 数据库迁移完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 数据库迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { addIsDefaultField };
