const { pool } = require('../config/db');

class Item {
  // 创建新子元素
  static async create(itemData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO items (category_id, name, thumbnail, front_image, back_image, status, content, is_default)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          itemData.category_id,
          itemData.name,
          itemData.thumbnail,
          itemData.front_image,
          itemData.back_image,
          itemData.status,
          itemData.content,
          itemData.is_default || false
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建子元素失败:', error);
      throw error;
    }
  }

  // 获取所有子元素
  static async findAll() {
    try {
      const [rows] = await pool.query(`
        SELECT i.*, c.category as category_name 
        FROM items i 
        LEFT JOIN categories c ON i.category_id = c.id 
        ORDER BY i.created_at DESC
      `);
      return rows;
    } catch (error) {
      console.error('获取子元素列表失败:', error);
      throw error;
    }
  }

  // 根据分类ID获取子元素
  static async findByCategoryId(categoryId) {
    try {
      const [rows] = await pool.query('SELECT * FROM items WHERE category_id = ? ORDER BY created_at DESC', [categoryId]);
      return rows;
    } catch (error) {
      console.error('获取分类下子元素失败:', error);
      throw error;
    }
  }

  // 根据ID获取子元素
  static async findById(id) {
    try {
      const [rows] = await pool.query(`
        SELECT i.*, c.category as category_name 
        FROM items i 
        LEFT JOIN categories c ON i.category_id = c.id 
        WHERE i.id = ?
      `, [id]);
      return rows[0];
    } catch (error) {
      console.error('获取子元素信息失败:', error);
      throw error;
    }
  }

  // 更新子元素信息
  static async update(id, itemData) {
    try {
      const [result] = await pool.query(
        `UPDATE items SET category_id = ?, name = ?, thumbnail = ?, front_image = ?,
         back_image = ?, status = ?, content = ?, is_default = ? WHERE id = ?`,
        [
          itemData.category_id,
          itemData.name,
          itemData.thumbnail,
          itemData.front_image,
          itemData.back_image,
          itemData.status,
          itemData.content,
          itemData.is_default || false,
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新子元素信息失败:', error);
      throw error;
    }
  }

  // 删除子元素
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM items WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除子元素失败:', error);
      throw error;
    }
  }

  // 获取所有默认项（每个大类一个）
  static async findDefaultItems() {
    try {
      const [rows] = await pool.query(`
        SELECT i.*, c.category as category_name, c.main_category
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        WHERE i.is_default = true
        ORDER BY c.main_category, i.created_at DESC
      `);
      return rows;
    } catch (error) {
      console.error('获取默认项失败:', error);
      throw error;
    }
  }

  // 根据大类获取默认项
  static async findDefaultByMainCategory(mainCategory) {
    try {
      const [rows] = await pool.query(`
        SELECT i.*, c.category as category_name, c.main_category
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        WHERE i.is_default = true AND c.main_category = ?
        ORDER BY i.created_at DESC
        LIMIT 1
      `, [mainCategory]);
      return rows[0];
    } catch (error) {
      console.error('根据大类获取默认项失败:', error);
      throw error;
    }
  }

  // 设置默认项（确保同一大类只有一个默认项）
  static async setAsDefault(itemId) {
    try {
      // 开始事务
      await pool.query('START TRANSACTION');

      // 1. 获取当前项目的分类信息
      const [itemRows] = await pool.query(`
        SELECT i.*, c.main_category
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        WHERE i.id = ?
      `, [itemId]);

      if (itemRows.length === 0) {
        throw new Error('项目不存在');
      }

      const item = itemRows[0];
      const mainCategory = item.main_category;

      // 2. 取消同一大类中所有其他项的默认状态
      await pool.query(`
        UPDATE items i
        LEFT JOIN categories c ON i.category_id = c.id
        SET i.is_default = false
        WHERE c.main_category = ? AND i.id != ?
      `, [mainCategory, itemId]);

      // 3. 设置当前项为默认
      await pool.query('UPDATE items SET is_default = true WHERE id = ?', [itemId]);

      // 提交事务
      await pool.query('COMMIT');

      return true;
    } catch (error) {
      // 回滚事务
      await pool.query('ROLLBACK');
      console.error('设置默认项失败:', error);
      throw error;
    }
  }

  // 取消默认项
  static async unsetAsDefault(itemId) {
    try {
      await pool.query('UPDATE items SET is_default = false WHERE id = ?', [itemId]);
      return true;
    } catch (error) {
      console.error('取消默认项失败:', error);
      throw error;
    }
  }
}

module.exports = Item;