{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox, ElLoading } from 'element-plus';\nimport { Plus, Search, Edit, Delete, Grid, Star } from '@element-plus/icons-vue';\nimport { categoryAPI, itemAPI } from '@/utils/api';\n\n// 使用环境变量获取\nconst API_BASE_URL = 'http://localhost:3000';\n\n// 创建图片URL的辅助函数\n\nexport default {\n  __name: 'CategoryList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const getImageUrl = imagePath => {\n      if (!imagePath) return '';\n      // 如果路径已经包含http，直接返回\n      if (imagePath.startsWith('http')) return imagePath;\n      // 否则拼接基础URL\n      return `${API_BASE_URL}/${imagePath.replace(/^\\/+/, '')}`;\n    };\n\n    // 响应式数据\n    const categories = ref([]);\n    const loading = ref(false);\n    const dialogVisible = ref(false);\n    const submitting = ref(false);\n    const isEdit = ref(false);\n    const formRef = ref();\n\n    // 子项相关数据\n    const items = ref([]);\n    const itemsLoading = ref(false);\n    const itemsDialogVisible = ref(false);\n    const itemDialogVisible = ref(false);\n    const itemSubmitting = ref(false);\n    const isItemEdit = ref(false);\n    const itemFormRef = ref();\n    const currentCategory = ref(null);\n\n    // 图片相关\n    const thumbnailFile = ref(null);\n    const frontImageFile = ref(null);\n    const backImageFile = ref(null);\n    const thumbnailPreview = ref('');\n    const frontImagePreview = ref('');\n    const backImagePreview = ref('');\n\n    // 搜索表单\n    const searchForm = reactive({\n      category: '',\n      main_category: ''\n    });\n\n    // 分类表单\n    const categoryForm = reactive({\n      id: '',\n      category: '',\n      main_category: '',\n      content: ''\n    });\n\n    // 子项表单\n    const itemForm = reactive({\n      id: '',\n      category_id: '',\n      name: '',\n      status: 'active',\n      content: '',\n      is_default: false\n    });\n\n    // 表单验证规则\n    const rules = {\n      category: [{\n        required: true,\n        message: '请输入分类名称',\n        trigger: 'blur'\n      }],\n      main_category: [{\n        required: true,\n        message: '请选择大类',\n        trigger: 'change'\n      }]\n    };\n    const itemRules = {\n      name: [{\n        required: true,\n        message: '请输入名称',\n        trigger: 'blur'\n      }]\n    };\n\n    // 计算属性\n    const dialogTitle = computed(() => isEdit.value ? '编辑分类' : '添加分类');\n    const itemDialogTitle = computed(() => isItemEdit.value ? '编辑子项' : '添加子项');\n\n    // 获取分类列表\n    const fetchCategories = async () => {\n      loading.value = true;\n      try {\n        const response = await categoryAPI.getAll(searchForm);\n        categories.value = response.data.data || response.data;\n      } catch (error) {\n        ElMessage.error('获取分类列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 显示添加对话框\n    const showAddDialog = () => {\n      resetForm();\n      dialogVisible.value = true;\n    };\n\n    // 编辑分类\n    const editCategory = row => {\n      isEdit.value = true;\n      categoryForm.id = row.id;\n      categoryForm.category = row.category;\n      categoryForm.main_category = row.main_category;\n      categoryForm.content = row.content;\n      dialogVisible.value = true;\n    };\n\n    // 删除分类\n    const deleteCategory = async row => {\n      try {\n        await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        await categoryAPI.delete(row.id);\n        ElMessage.success('删除成功');\n        fetchCategories();\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error('删除失败');\n        }\n      }\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!formRef.value) return;\n      try {\n        await formRef.value.validate();\n        submitting.value = true;\n        if (isEdit.value) {\n          await categoryAPI.update(categoryForm.id, categoryForm);\n          ElMessage.success('更新成功');\n        } else {\n          await categoryAPI.create(categoryForm);\n          ElMessage.success('创建成功');\n        }\n        dialogVisible.value = false;\n        fetchCategories();\n      } catch (error) {\n        ElMessage.error(isEdit.value ? '更新失败' : '创建失败');\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      isEdit.value = false;\n      categoryForm.id = '';\n      categoryForm.category = '';\n      categoryForm.main_category = '';\n      categoryForm.content = '';\n      if (formRef.value) {\n        formRef.value.resetFields();\n      }\n    };\n\n    // 重置搜索\n    const resetSearch = () => {\n      searchForm.category = '';\n      searchForm.main_category = '';\n      fetchCategories();\n    };\n\n    // 显示子项管理对话框\n    const showItemsDialog = async category => {\n      currentCategory.value = category;\n      itemsDialogVisible.value = true;\n      await fetchItems(category.id);\n    };\n\n    // 获取子项列表\n    const fetchItems = async categoryId => {\n      itemsLoading.value = true;\n      try {\n        const response = await itemAPI.getByCategory(categoryId);\n        items.value = response.data.data || response.data;\n      } catch (error) {\n        ElMessage.error('获取子项列表失败');\n      } finally {\n        itemsLoading.value = false;\n      }\n    };\n\n    // 显示添加子项对话框\n    const showAddItemDialog = () => {\n      isItemEdit.value = false;\n      itemForm.category_id = currentCategory.value.id;\n      itemDialogVisible.value = true;\n    };\n\n    // 编辑子项 - 使用辅助函数\n    const editItem = row => {\n      isItemEdit.value = true;\n      itemForm.id = row.id;\n      itemForm.category_id = row.category_id;\n      itemForm.name = row.name;\n      itemForm.status = row.status;\n      itemForm.content = row.content;\n      itemForm.is_default = row.is_default == 0 ? false : true;\n\n      // 图片回显 - 使用辅助函数\n      thumbnailPreview.value = getImageUrl(row.thumbnail);\n      frontImagePreview.value = getImageUrl(row.front_image);\n      backImagePreview.value = getImageUrl(row.back_image);\n      itemDialogVisible.value = true;\n    };\n\n    // 删除子项 - 添加二次确认和图片删除\n    const deleteItem = async row => {\n      try {\n        await ElMessageBox.confirm(`确定要删除子项 \"${row.name}\" 吗？删除后将无法恢复，相关图片也会被删除。`, '删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: true\n        });\n\n        // 显示删除进度\n        const loadingInstance = ElLoading.service({\n          lock: true,\n          text: '正在删除...',\n          background: 'rgba(0, 0, 0, 0.7)'\n        });\n        try {\n          await itemAPI.delete(row.id);\n          ElMessage.success('删除成功');\n          fetchItems(currentCategory.value.id);\n        } catch (error) {\n          console.error('删除失败:', error);\n          ElMessage.error('删除失败，请重试');\n        } finally {\n          loadingInstance.close();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error('删除操作失败');\n        }\n      }\n    };\n\n    // 处理图片上传\n    const handleThumbnailChange = file => {\n      thumbnailFile.value = file.raw;\n      thumbnailPreview.value = URL.createObjectURL(file.raw);\n    };\n    const handleFrontImageChange = file => {\n      frontImageFile.value = file.raw;\n      frontImagePreview.value = URL.createObjectURL(file.raw);\n    };\n    const handleBackImageChange = file => {\n      backImageFile.value = file.raw;\n      backImagePreview.value = URL.createObjectURL(file.raw);\n    };\n\n    // 提交子项表单\n    const submitItemForm = async () => {\n      if (!itemFormRef.value) return;\n      try {\n        await itemFormRef.value.validate();\n        if (!isItemEdit.value && (!thumbnailFile.value || !frontImageFile.value || !backImageFile.value)) {\n          ElMessage.error('请上传所有必需的图片');\n          return;\n        }\n        itemSubmitting.value = true;\n        const formData = new FormData();\n        formData.append('category_id', itemForm.category_id);\n        formData.append('name', itemForm.name);\n        formData.append('status', itemForm.status);\n        formData.append('content', itemForm.content);\n\n        // 只在新建模式下处理默认状态，编辑模式下通过单独的API处理\n        if (!isItemEdit.value) {\n          formData.append('is_default', itemForm.is_default);\n        }\n        if (thumbnailFile.value) {\n          formData.append('thumbnail', thumbnailFile.value);\n        }\n        if (frontImageFile.value) {\n          formData.append('front_image', frontImageFile.value);\n        }\n        if (backImageFile.value) {\n          formData.append('back_image', backImageFile.value);\n        }\n        if (isItemEdit.value) {\n          await itemAPI.update(itemForm.id, formData);\n          ElMessage.success('更新成功');\n        } else {\n          await itemAPI.create(formData);\n          ElMessage.success('创建成功');\n        }\n        itemDialogVisible.value = false;\n        fetchItems(currentCategory.value.id);\n      } catch (error) {\n        ElMessage.error(isItemEdit.value ? '更新失败' : '创建失败');\n      } finally {\n        itemSubmitting.value = false;\n      }\n    };\n\n    // 处理默认状态变化\n    const handleDefaultChange = async value => {\n      // 新建模式下的提示\n      if (!isItemEdit.value) {\n        if (value) {\n          ElMessage.info('保存后将设置为默认项，同一大类的其他默认项将被取消');\n        }\n        return;\n      }\n\n      // 编辑模式下立即调用API更新\n      if (isItemEdit.value && itemForm.id) {\n        try {\n          await itemAPI.toggleDefault(itemForm.id, value);\n          ElMessage.success(value ? '已设置为默认项' : '已取消默认状态');\n\n          // 刷新当前分类的子项列表\n          if (currentCategory.value) {\n            await fetchItems(currentCategory.value.id);\n          }\n        } catch (error) {\n          console.error('更新默认状态失败:', error);\n          ElMessage.error('更新默认状态失败');\n          // 恢复原状态\n          itemForm.is_default = !value;\n        }\n      }\n    };\n\n    // 重置子项表单 - 清空图片预览\n    const resetItemForm = () => {\n      isItemEdit.value = false;\n      itemForm.id = '';\n      itemForm.category_id = '';\n      itemForm.name = '';\n      itemForm.status = 'active';\n      itemForm.content = '';\n      itemForm.is_default = false;\n\n      // 清空图片文件和预览\n      thumbnailFile.value = null;\n      frontImageFile.value = null;\n      backImageFile.value = null;\n      thumbnailPreview.value = '';\n      frontImagePreview.value = '';\n      backImagePreview.value = '';\n      if (itemFormRef.value) {\n        itemFormRef.value.resetFields();\n      }\n    };\n\n    // 重置子项对话框\n    const resetItemsDialog = () => {\n      items.value = [];\n      currentCategory.value = null;\n    };\n\n    // 格式化日期\n    const formatDate = date => {\n      return new Date(date).toLocaleString();\n    };\n\n    // 获取大类标签类型\n    const getMainCategoryType = mainCategory => {\n      const typeMap = {\n        '头部': 'primary',\n        '发型': 'success',\n        '躯干': 'warning',\n        '腿部': 'danger',\n        '配件': 'info'\n      };\n      return typeMap[mainCategory] || 'default';\n    };\n\n    // 图片预览功能\n    const previewImage = src => {\n      // Element Plus 的图片预览会自动处理\n      console.log('预览图片:', src);\n    };\n\n    // 页面加载时获取数据\n    onMounted(() => {\n      fetchCategories();\n    });\n    const __returned__ = {\n      API_BASE_URL,\n      getImageUrl,\n      categories,\n      loading,\n      dialogVisible,\n      submitting,\n      isEdit,\n      formRef,\n      items,\n      itemsLoading,\n      itemsDialogVisible,\n      itemDialogVisible,\n      itemSubmitting,\n      isItemEdit,\n      itemFormRef,\n      currentCategory,\n      thumbnailFile,\n      frontImageFile,\n      backImageFile,\n      thumbnailPreview,\n      frontImagePreview,\n      backImagePreview,\n      searchForm,\n      categoryForm,\n      itemForm,\n      rules,\n      itemRules,\n      dialogTitle,\n      itemDialogTitle,\n      fetchCategories,\n      showAddDialog,\n      editCategory,\n      deleteCategory,\n      submitForm,\n      resetForm,\n      resetSearch,\n      showItemsDialog,\n      fetchItems,\n      showAddItemDialog,\n      editItem,\n      deleteItem,\n      handleThumbnailChange,\n      handleFrontImageChange,\n      handleBackImageChange,\n      submitItemForm,\n      handleDefaultChange,\n      resetItemForm,\n      resetItemsDialog,\n      formatDate,\n      getMainCategoryType,\n      previewImage,\n      ref,\n      reactive,\n      onMounted,\n      computed,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get ElLoading() {\n        return ElLoading;\n      },\n      get Plus() {\n        return Plus;\n      },\n      get Search() {\n        return Search;\n      },\n      get Edit() {\n        return Edit;\n      },\n      get Delete() {\n        return Delete;\n      },\n      get Grid() {\n        return Grid;\n      },\n      get Star() {\n        return Star;\n      },\n      get categoryAPI() {\n        return categoryAPI;\n      },\n      get itemAPI() {\n        return itemAPI;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "ElMessage", "ElMessageBox", "ElLoading", "Plus", "Search", "Edit", "Delete", "Grid", "Star", "categoryAPI", "itemAPI", "API_BASE_URL", "getImageUrl", "imagePath", "startsWith", "replace", "categories", "loading", "dialogVisible", "submitting", "isEdit", "formRef", "items", "itemsLoading", "itemsDialogVisible", "itemDialogVisible", "itemSubmitting", "isItemEdit", "itemFormRef", "currentCategory", "thumbnailFile", "frontImageFile", "backImageFile", "thumbnailPreview", "frontImagePreview", "backImagePreview", "searchForm", "category", "main_category", "categoryForm", "id", "content", "itemForm", "category_id", "name", "status", "is_default", "rules", "required", "message", "trigger", "itemRules", "dialogTitle", "value", "itemDialogTitle", "fetchCategories", "response", "getAll", "data", "error", "showAddDialog", "resetForm", "editCategory", "row", "deleteCategory", "confirm", "confirmButtonText", "cancelButtonText", "type", "delete", "success", "submitForm", "validate", "update", "create", "resetFields", "resetSearch", "showItemsDialog", "fetchItems", "categoryId", "getByCategory", "showAddItemDialog", "editItem", "thumbnail", "front_image", "back_image", "deleteItem", "dangerouslyUseHTMLString", "loadingInstance", "service", "lock", "text", "background", "console", "close", "handleThumbnailChange", "file", "raw", "URL", "createObjectURL", "handleFrontImageChange", "handleBackImageChange", "submitItemForm", "formData", "FormData", "append", "handleDefaultChange", "info", "to<PERSON><PERSON><PERSON><PERSON>", "resetItemForm", "resetItemsDialog", "formatDate", "date", "Date", "toLocaleString", "getMainCategoryType", "mainCategory", "typeMap", "previewImage", "src", "log"], "sources": ["D:/admin/202506/乐高/乐高后台/后台管理系统v2/后台管理系统/ms/src/views/categories/CategoryList.vue"], "sourcesContent": ["<template>\n  <div class=\"category-list\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>分类管理</h2>\n      <el-button type=\"primary\" @click=\"showAddDialog\">\n        <el-icon><Plus /></el-icon>\n        添加分类\n      </el-button>\n    </div>\n\n    <!-- 搜索区域 -->\n    <div class=\"search-area\">\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n        <el-form-item label=\"分类名称\">\n          <el-input v-model=\"searchForm.category\" placeholder=\"请输入分类名称\" clearable style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item label=\"大类\">\n          <el-select v-model=\"searchForm.main_category\" placeholder=\"请选择大类\" clearable style=\"width: 150px\">\n            <el-option label=\"头部\" value=\"头部\" />\n            <el-option label=\"发型\" value=\"发型\" />\n            <el-option label=\"躯干\" value=\"躯干\" />\n            <el-option label=\"腿部\" value=\"腿部\" />\n            <el-option label=\"配件\" value=\"配件\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"fetchCategories\">\n            <el-icon><Search /></el-icon>\n            搜索\n          </el-button>\n          <el-button @click=\"resetSearch\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <!-- 表格区域 -->\n    <div class=\"table-container\">\n      <el-table :data=\"categories\" v-loading=\"loading\" stripe border>\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column prop=\"category\" label=\"分类名称\" min-width=\"150\" />\n        <el-table-column prop=\"main_category\" label=\"大类\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-tag :type=\"getMainCategoryType(row.main_category)\">\n              {{ row.main_category }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"content\" label=\"内容描述\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"180\">\n          <template #default=\"{ row }\">\n            {{ formatDate(row.created_at) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"子项\" width=\"120\">\n          <template #default=\"{ row }\">\n            <el-button type=\"info\" size=\"small\" @click=\"showItemsDialog(row)\">\n              <el-icon><Grid /></el-icon>\n              管理子项\n            </el-button>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\n          <template #default=\"{ row }\">\n            <el-button type=\"primary\" size=\"small\" @click=\"editCategory(row)\">\n              <el-icon><Edit /></el-icon>\n              编辑\n            </el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"deleteCategory(row)\">\n              <el-icon><Delete /></el-icon>\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 添加/编辑分类对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      v-model=\"dialogVisible\"\n      width=\"500px\"\n      @close=\"resetForm\"\n    >\n      <el-form :model=\"categoryForm\" :rules=\"rules\" ref=\"formRef\" label-width=\"100px\">\n        <el-form-item label=\"分类名称\" prop=\"category\">\n          <el-input v-model=\"categoryForm.category\" placeholder=\"请输入分类名称\" />\n        </el-form-item>\n        <el-form-item label=\"大类\" prop=\"main_category\">\n          <el-select v-model=\"categoryForm.main_category\" placeholder=\"请选择大类\" style=\"width: 100%\">\n            <el-option label=\"头部\" value=\"头部\" />\n            <el-option label=\"发型\" value=\"发型\" />\n            <el-option label=\"躯干\" value=\"躯干\" />\n            <el-option label=\"腿部\" value=\"腿部\" />\n            <el-option label=\"配件\" value=\"配件\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"内容描述\" prop=\"content\">\n          <el-input\n            v-model=\"categoryForm.content\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入内容描述\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 子项管理对话框 -->\n    <el-dialog\n      title=\"子项管理\"\n      v-model=\"itemsDialogVisible\"\n      width=\"80%\"\n      @close=\"resetItemsDialog\"\n    >\n      <div class=\"items-header\">\n        <h3>{{ currentCategory?.category }} - 子项列表</h3>\n        <el-button type=\"primary\" @click=\"showAddItemDialog\">\n          <el-icon><Plus /></el-icon>\n          添加子项\n        </el-button>\n      </div>\n\n      <el-table :data=\"items\" v-loading=\"itemsLoading\" stripe border>\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column label=\"缩略图\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-image\n              \n              :src=\"getImageUrl(row.thumbnail)\"\n              :preview-src-list=\"[getImageUrl(row.thumbnail)]\"\n              style=\"width: 60px; height: 60px\"\n              fit=\"cover\"\n              @click=\"previewImage(getImageUrl(row.thumbnail))\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"前置图\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-image\n              :src=\"getImageUrl(row.front_image)\"\n              :preview-src-list=\"[getImageUrl(row.front_image)]\"\n              style=\"width: 60px; height: 60px\"\n              fit=\"cover\"\n              @click=\"previewImage(getImageUrl(row.front_image))\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"后置图\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-image\n            \n              :src=\"getImageUrl(row.back_image)\"\n              :preview-src-list=\"[getImageUrl(row.back_image)]\"\n              style=\"width: 60px; height: 60px\"\n              fit=\"cover\"\n              @click=\"previewImage(getImageUrl(row.back_image))\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"name\" label=\"名称\" min-width=\"150\" />\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-tag :type=\"row.status === 'active' ? 'success' : 'danger'\">\n              {{ row.status === 'active' ? '启用' : '禁用' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"默认\" width=\"80\">\n          <template #default=\"{ row }\">\n            <el-tag v-if=\"row.is_default\" type=\"warning\" size=\"small\">\n              <el-icon><Star /></el-icon>\n              默认\n            </el-tag>\n            <span v-else class=\"text-gray\">-</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"180\">\n          <template #default=\"{ row }\">\n            {{ formatDate(row.created_at) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"{ row }\">\n            <el-button type=\"primary\" size=\"small\" @click=\"editItem(row)\">编辑</el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"deleteItem(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n\n    <!-- 添加/编辑子项对话框 -->\n    <el-dialog\n    append-to-body\t\n      :title=\"itemDialogTitle\"\n      v-model=\"itemDialogVisible\"\n      width=\"600px\"\n      @close=\"resetItemForm\"\n    >\n      <el-form :model=\"itemForm\" :rules=\"itemRules\" ref=\"itemFormRef\" label-width=\"100px\">\n        <el-form-item label=\"名称\" prop=\"name\">\n          <el-input v-model=\"itemForm.name\" placeholder=\"请输入名称\" />\n        </el-form-item>\n        <el-form-item label=\"缩略图\" prop=\"thumbnail\">\n          <el-upload\n            :auto-upload=\"false\"\n            :on-change=\"handleThumbnailChange\"\n            :show-file-list=\"false\"\n            accept=\"image/*\"\n          >\n            <el-button type=\"primary\">选择缩略图</el-button>\n          </el-upload>\n          <div v-if=\"thumbnailPreview\" class=\"image-preview\">\n            <el-image :src=\"thumbnailPreview\" style=\"width: 100px; height: 100px\" fit=\"cover\" />\n          </div>\n        </el-form-item>\n        <el-form-item label=\"前置图\" prop=\"front_image\">\n          <el-upload\n            :auto-upload=\"false\"\n            :on-change=\"handleFrontImageChange\"\n            :show-file-list=\"false\"\n            accept=\"image/*\"\n          >\n            <el-button type=\"primary\">选择前置图</el-button>\n          </el-upload>\n          <div v-if=\"frontImagePreview\" class=\"image-preview\">\n            <el-image :src=\"frontImagePreview\" style=\"width: 100px; height: 100px\" fit=\"cover\" />\n          </div>\n        </el-form-item>\n        <el-form-item label=\"后置图\" prop=\"back_image\">\n          <el-upload\n            :auto-upload=\"false\"\n            :on-change=\"handleBackImageChange\"\n            :show-file-list=\"false\"\n            accept=\"image/*\"\n          >\n            <el-button type=\"primary\">选择后置图</el-button>\n          </el-upload>\n          <div v-if=\"backImagePreview\" class=\"image-preview\">\n            <el-image :src=\"backImagePreview\" style=\"width: 100px; height: 100px\" fit=\"cover\" />\n          </div>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"itemForm.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"inactive\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"是否默认\" prop=\"is_default\">\n          <el-switch\n            v-model=\"itemForm.is_default\"\n            active-text=\"是\"\n            inactive-text=\"否\"\n            @change=\"handleDefaultChange\"\n          />\n          <div class=\"form-tip\">设为默认后，此项将在前端页面初始化时自动显示。每个大类只能有一个默认项。</div>\n        </el-form-item>\n        <el-form-item label=\"内容描述\" prop=\"content\">\n          <el-input\n            v-model=\"itemForm.content\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入内容描述\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <el-button @click=\"itemDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitItemForm\" :loading=\"itemSubmitting\">确定</el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox, ElLoading } from 'element-plus'\nimport { Plus, Search, Edit, Delete, Grid, Star } from '@element-plus/icons-vue'\nimport { categoryAPI, itemAPI } from '@/utils/api'\n\n// 使用环境变量获取\nconst API_BASE_URL = 'http://localhost:3000'\n\n// 创建图片URL的辅助函数\nconst getImageUrl = (imagePath) => {\n  if (!imagePath) return ''\n  // 如果路径已经包含http，直接返回\n  if (imagePath.startsWith('http')) return imagePath\n  // 否则拼接基础URL\n  return `${API_BASE_URL}/${imagePath.replace(/^\\/+/, '')}`\n}\n\n// 响应式数据\nconst categories = ref([])\nconst loading = ref(false)\nconst dialogVisible = ref(false)\nconst submitting = ref(false)\nconst isEdit = ref(false)\nconst formRef = ref()\n\n// 子项相关数据\nconst items = ref([])\nconst itemsLoading = ref(false)\nconst itemsDialogVisible = ref(false)\nconst itemDialogVisible = ref(false)\nconst itemSubmitting = ref(false)\nconst isItemEdit = ref(false)\nconst itemFormRef = ref()\nconst currentCategory = ref(null)\n\n// 图片相关\nconst thumbnailFile = ref(null)\nconst frontImageFile = ref(null)\nconst backImageFile = ref(null)\nconst thumbnailPreview = ref('')\nconst frontImagePreview = ref('')\nconst backImagePreview = ref('')\n\n// 搜索表单\nconst searchForm = reactive({\n  category: '',\n  main_category: ''\n})\n\n// 分类表单\nconst categoryForm = reactive({\n  id: '',\n  category: '',\n  main_category: '',\n  content: ''\n})\n\n// 子项表单\nconst itemForm = reactive({\n  id: '',\n  category_id: '',\n  name: '',\n  status: 'active',\n  content: '',\n  is_default: false\n})\n\n// 表单验证规则\nconst rules = {\n  category: [\n    { required: true, message: '请输入分类名称', trigger: 'blur' }\n  ],\n  main_category: [\n    { required: true, message: '请选择大类', trigger: 'change' }\n  ]\n}\n\nconst itemRules = {\n  name: [\n    { required: true, message: '请输入名称', trigger: 'blur' }\n  ]\n}\n\n// 计算属性\nconst dialogTitle = computed(() => isEdit.value ? '编辑分类' : '添加分类')\nconst itemDialogTitle = computed(() => isItemEdit.value ? '编辑子项' : '添加子项')\n\n// 获取分类列表\nconst fetchCategories = async () => {\n  loading.value = true\n  try {\n    const response = await categoryAPI.getAll(searchForm)\n    categories.value = response.data.data || response.data\n  } catch (error) {\n    ElMessage.error('获取分类列表失败')\n  } finally {\n    loading.value = false\n  }\n}\n\n// 显示添加对话框\nconst showAddDialog = () => {\n  resetForm()\n  dialogVisible.value = true\n}\n\n// 编辑分类\nconst editCategory = (row) => {\n  isEdit.value = true\n  categoryForm.id = row.id\n  categoryForm.category = row.category\n  categoryForm.main_category = row.main_category\n  categoryForm.content = row.content\n  dialogVisible.value = true\n}\n\n// 删除分类\nconst deleteCategory = async (row) => {\n  try {\n    await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n    \n    await categoryAPI.delete(row.id)\n    ElMessage.success('删除成功')\n    fetchCategories()\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除失败')\n    }\n  }\n}\n\n// 提交表单\nconst submitForm = async () => {\n  if (!formRef.value) return\n  \n  try {\n    await formRef.value.validate()\n    submitting.value = true\n    \n    if (isEdit.value) {\n      await categoryAPI.update(categoryForm.id, categoryForm)\n      ElMessage.success('更新成功')\n    } else {\n      await categoryAPI.create(categoryForm)\n      ElMessage.success('创建成功')\n    }\n    \n    dialogVisible.value = false\n    fetchCategories()\n  } catch (error) {\n    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')\n  } finally {\n    submitting.value = false\n  }\n}\n\n// 重置表单\nconst resetForm = () => {\n  isEdit.value = false\n  categoryForm.id = ''\n  categoryForm.category = ''\n  categoryForm.main_category = ''\n  categoryForm.content = ''\n  if (formRef.value) {\n    formRef.value.resetFields()\n  }\n}\n\n// 重置搜索\nconst resetSearch = () => {\n  searchForm.category = ''\n  searchForm.main_category = ''\n  fetchCategories()\n}\n\n// 显示子项管理对话框\nconst showItemsDialog = async (category) => {\n  currentCategory.value = category\n  itemsDialogVisible.value = true\n  await fetchItems(category.id)\n}\n\n// 获取子项列表\nconst fetchItems = async (categoryId) => {\n  itemsLoading.value = true\n  try {\n    const response = await itemAPI.getByCategory(categoryId)\n    items.value = response.data.data || response.data\n  } catch (error) {\n    ElMessage.error('获取子项列表失败')\n  } finally {\n    itemsLoading.value = false\n  }\n}\n\n// 显示添加子项对话框\nconst showAddItemDialog = () => {\n  isItemEdit.value = false\n  itemForm.category_id = currentCategory.value.id\n  itemDialogVisible.value = true\n}\n\n// 编辑子项 - 使用辅助函数\nconst editItem = (row) => {\n  isItemEdit.value = true\n  itemForm.id = row.id\n  itemForm.category_id = row.category_id\n  itemForm.name = row.name\n  itemForm.status = row.status\n  itemForm.content = row.content\n  itemForm.is_default = row.is_default == 0 ? false : true\n\n  // 图片回显 - 使用辅助函数\n  thumbnailPreview.value = getImageUrl(row.thumbnail)\n  frontImagePreview.value = getImageUrl(row.front_image)\n  backImagePreview.value = getImageUrl(row.back_image)\n\n  itemDialogVisible.value = true\n}\n\n// 删除子项 - 添加二次确认和图片删除\nconst deleteItem = async (row) => {\n  try {\n    await ElMessageBox.confirm(\n      `确定要删除子项 \"${row.name}\" 吗？删除后将无法恢复，相关图片也会被删除。`, \n      '删除确认', \n      {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: true\n      }\n    )\n    \n    // 显示删除进度\n    const loadingInstance = ElLoading.service({\n      lock: true,\n      text: '正在删除...',\n      background: 'rgba(0, 0, 0, 0.7)'\n    })\n    \n    try {\n      await itemAPI.delete(row.id)\n      ElMessage.success('删除成功')\n      fetchItems(currentCategory.value.id)\n    } catch (error) {\n      console.error('删除失败:', error)\n      ElMessage.error('删除失败，请重试')\n    } finally {\n      loadingInstance.close()\n    }\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除操作失败')\n    }\n  }\n}\n\n// 处理图片上传\nconst handleThumbnailChange = (file) => {\n  thumbnailFile.value = file.raw\n  thumbnailPreview.value = URL.createObjectURL(file.raw)\n}\n\nconst handleFrontImageChange = (file) => {\n  frontImageFile.value = file.raw\n  frontImagePreview.value = URL.createObjectURL(file.raw)\n}\n\nconst handleBackImageChange = (file) => {\n  backImageFile.value = file.raw\n  backImagePreview.value = URL.createObjectURL(file.raw)\n}\n\n// 提交子项表单\nconst submitItemForm = async () => {\n  if (!itemFormRef.value) return\n  \n  try {\n    await itemFormRef.value.validate()\n    \n    if (!isItemEdit.value && (!thumbnailFile.value || !frontImageFile.value || !backImageFile.value)) {\n      ElMessage.error('请上传所有必需的图片')\n      return\n    }\n    \n    itemSubmitting.value = true\n    \n    const formData = new FormData()\n    formData.append('category_id', itemForm.category_id)\n    formData.append('name', itemForm.name)\n    formData.append('status', itemForm.status)\n    formData.append('content', itemForm.content)\n\n    // 只在新建模式下处理默认状态，编辑模式下通过单独的API处理\n    if (!isItemEdit.value) {\n      formData.append('is_default', itemForm.is_default)\n    }\n    \n    if (thumbnailFile.value) {\n      formData.append('thumbnail', thumbnailFile.value)\n    }\n    if (frontImageFile.value) {\n      formData.append('front_image', frontImageFile.value)\n    }\n    if (backImageFile.value) {\n      formData.append('back_image', backImageFile.value)\n    }\n    \n    if (isItemEdit.value) {\n      await itemAPI.update(itemForm.id, formData)\n      ElMessage.success('更新成功')\n    } else {\n      await itemAPI.create(formData)\n      ElMessage.success('创建成功')\n    }\n    \n    itemDialogVisible.value = false\n    fetchItems(currentCategory.value.id)\n  } catch (error) {\n    ElMessage.error(isItemEdit.value ? '更新失败' : '创建失败')\n  } finally {\n    itemSubmitting.value = false\n  }\n}\n\n// 处理默认状态变化\nconst handleDefaultChange = async (value) => {\n  // 新建模式下的提示\n  if (!isItemEdit.value) {\n    if (value) {\n      ElMessage.info('保存后将设置为默认项，同一大类的其他默认项将被取消')\n    }\n    return\n  }\n\n  // 编辑模式下立即调用API更新\n  if (isItemEdit.value && itemForm.id) {\n    try {\n      await itemAPI.toggleDefault(itemForm.id, value)\n      ElMessage.success(value ? '已设置为默认项' : '已取消默认状态')\n\n      // 刷新当前分类的子项列表\n      if (currentCategory.value) {\n        await fetchItems(currentCategory.value.id)\n      }\n    } catch (error) {\n      console.error('更新默认状态失败:', error)\n      ElMessage.error('更新默认状态失败')\n      // 恢复原状态\n      itemForm.is_default = !value\n    }\n  }\n}\n\n// 重置子项表单 - 清空图片预览\nconst resetItemForm = () => {\n  isItemEdit.value = false\n  itemForm.id = ''\n  itemForm.category_id = ''\n  itemForm.name = ''\n  itemForm.status = 'active'\n  itemForm.content = ''\n  itemForm.is_default = false\n\n  // 清空图片文件和预览\n  thumbnailFile.value = null\n  frontImageFile.value = null\n  backImageFile.value = null\n  thumbnailPreview.value = ''\n  frontImagePreview.value = ''\n  backImagePreview.value = ''\n\n  if (itemFormRef.value) {\n    itemFormRef.value.resetFields()\n  }\n}\n\n// 重置子项对话框\nconst resetItemsDialog = () => {\n  items.value = []\n  currentCategory.value = null\n}\n\n// 格式化日期\nconst formatDate = (date) => {\n  return new Date(date).toLocaleString()\n}\n\n// 获取大类标签类型\nconst getMainCategoryType = (mainCategory) => {\n  const typeMap = {\n    '头部': 'primary',\n    '发型': 'success',\n    '躯干': 'warning',\n    '腿部': 'danger',\n    '配件': 'info'\n  }\n  return typeMap[mainCategory] || 'default'\n}\n\n// 图片预览功能\nconst previewImage = (src) => {\n  // Element Plus 的图片预览会自动处理\n  console.log('预览图片:', src)\n}\n\n// 页面加载时获取数据\nonMounted(() => {\n  fetchCategories()\n})\n</script>\n\n<style scoped>\n.category-list {\n  padding: 24px;\n  height: 100%;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e8eaec;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n  font-size: 20px;\n  font-weight: 500;\n}\n\n.search-area {\n  background-color: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.search-form {\n  margin: 0;\n}\n\n.table-container {\n  background-color: #fff;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.items-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.items-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.image-preview-container {\n  text-align: center;\n}\n\n.image-preview {\n  margin-top: 8px;\n}\n\n.el-imag\ne {\n  cursor: pointer;\n  border-radius: 4px;\n  border: 1px solid #dcdfe6;\n}\n\n.el-image:hover {\n  border-color: #409eff;\n}\n\n.form-tip {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 5px;\n  line-height: 1.4;\n}\n\n.text-gray {\n  color: #c0c4cc;\n  font-size: 12px;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;;AAuRA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAI;AACvD,SAASC,SAAS,EAAEC,YAAY,EAAEC,SAAS,QAAQ,cAAa;AAChE,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAQ,yBAAwB;AAC/E,SAASC,WAAW,EAAEC,OAAO,QAAQ,aAAY;;AAEjD;AACA,MAAMC,YAAY,GAAG,uBAAsB;;AAE3C;;;;;;;;IACA,MAAMC,WAAW,GAAIC,SAAS,IAAK;MACjC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAC;MACxB;MACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAQ;MACjD;MACA,OAAO,GAAGF,YAAY,IAAIE,SAAS,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAC;IAC1D;;IAEA;IACA,MAAMC,UAAU,GAAGpB,GAAG,CAAC,EAAE;IACzB,MAAMqB,OAAO,GAAGrB,GAAG,CAAC,KAAK;IACzB,MAAMsB,aAAa,GAAGtB,GAAG,CAAC,KAAK;IAC/B,MAAMuB,UAAU,GAAGvB,GAAG,CAAC,KAAK;IAC5B,MAAMwB,MAAM,GAAGxB,GAAG,CAAC,KAAK;IACxB,MAAMyB,OAAO,GAAGzB,GAAG,CAAC;;IAEpB;IACA,MAAM0B,KAAK,GAAG1B,GAAG,CAAC,EAAE;IACpB,MAAM2B,YAAY,GAAG3B,GAAG,CAAC,KAAK;IAC9B,MAAM4B,kBAAkB,GAAG5B,GAAG,CAAC,KAAK;IACpC,MAAM6B,iBAAiB,GAAG7B,GAAG,CAAC,KAAK;IACnC,MAAM8B,cAAc,GAAG9B,GAAG,CAAC,KAAK;IAChC,MAAM+B,UAAU,GAAG/B,GAAG,CAAC,KAAK;IAC5B,MAAMgC,WAAW,GAAGhC,GAAG,CAAC;IACxB,MAAMiC,eAAe,GAAGjC,GAAG,CAAC,IAAI;;IAEhC;IACA,MAAMkC,aAAa,GAAGlC,GAAG,CAAC,IAAI;IAC9B,MAAMmC,cAAc,GAAGnC,GAAG,CAAC,IAAI;IAC/B,MAAMoC,aAAa,GAAGpC,GAAG,CAAC,IAAI;IAC9B,MAAMqC,gBAAgB,GAAGrC,GAAG,CAAC,EAAE;IAC/B,MAAMsC,iBAAiB,GAAGtC,GAAG,CAAC,EAAE;IAChC,MAAMuC,gBAAgB,GAAGvC,GAAG,CAAC,EAAE;;IAE/B;IACA,MAAMwC,UAAU,GAAGvC,QAAQ,CAAC;MAC1BwC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE;IACjB,CAAC;;IAED;IACA,MAAMC,YAAY,GAAG1C,QAAQ,CAAC;MAC5B2C,EAAE,EAAE,EAAE;MACNH,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBG,OAAO,EAAE;IACX,CAAC;;IAED;IACA,MAAMC,QAAQ,GAAG7C,QAAQ,CAAC;MACxB2C,EAAE,EAAE,EAAE;MACNG,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,QAAQ;MAChBJ,OAAO,EAAE,EAAE;MACXK,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,KAAK,GAAG;MACZV,QAAQ,EAAE,CACR;QAAEW,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,EACvD;MACDZ,aAAa,EAAE,CACb;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS;IAE1D;IAEA,MAAMC,SAAS,GAAG;MAChBP,IAAI,EAAE,CACJ;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO;IAExD;;IAEA;IACA,MAAME,WAAW,GAAGrD,QAAQ,CAAC,MAAMqB,MAAM,CAACiC,KAAK,GAAG,MAAM,GAAG,MAAM;IACjE,MAAMC,eAAe,GAAGvD,QAAQ,CAAC,MAAM4B,UAAU,CAAC0B,KAAK,GAAG,MAAM,GAAG,MAAM;;IAEzE;IACA,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClCtC,OAAO,CAACoC,KAAK,GAAG,IAAG;MACnB,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAM/C,WAAW,CAACgD,MAAM,CAACrB,UAAU;QACpDpB,UAAU,CAACqC,KAAK,GAAGG,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAG;MACvD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd3D,SAAS,CAAC2D,KAAK,CAAC,UAAU;MAC5B,CAAC,SAAS;QACR1C,OAAO,CAACoC,KAAK,GAAG,KAAI;MACtB;IACF;;IAEA;IACA,MAAMO,aAAa,GAAGA,CAAA,KAAM;MAC1BC,SAAS,CAAC;MACV3C,aAAa,CAACmC,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAMS,YAAY,GAAIC,GAAG,IAAK;MAC5B3C,MAAM,CAACiC,KAAK,GAAG,IAAG;MAClBd,YAAY,CAACC,EAAE,GAAGuB,GAAG,CAACvB,EAAC;MACvBD,YAAY,CAACF,QAAQ,GAAG0B,GAAG,CAAC1B,QAAO;MACnCE,YAAY,CAACD,aAAa,GAAGyB,GAAG,CAACzB,aAAY;MAC7CC,YAAY,CAACE,OAAO,GAAGsB,GAAG,CAACtB,OAAM;MACjCvB,aAAa,CAACmC,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAMW,cAAc,GAAG,MAAOD,GAAG,IAAK;MACpC,IAAI;QACF,MAAM9D,YAAY,CAACgE,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE;UAC9CC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CAAC;QAED,MAAM3D,WAAW,CAAC4D,MAAM,CAACN,GAAG,CAACvB,EAAE;QAC/BxC,SAAS,CAACsE,OAAO,CAAC,MAAM;QACxBf,eAAe,CAAC;MAClB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtB3D,SAAS,CAAC2D,KAAK,CAAC,MAAM;QACxB;MACF;IACF;;IAEA;IACA,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAAClD,OAAO,CAACgC,KAAK,EAAE;MAEpB,IAAI;QACF,MAAMhC,OAAO,CAACgC,KAAK,CAACmB,QAAQ,CAAC;QAC7BrD,UAAU,CAACkC,KAAK,GAAG,IAAG;QAEtB,IAAIjC,MAAM,CAACiC,KAAK,EAAE;UAChB,MAAM5C,WAAW,CAACgE,MAAM,CAAClC,YAAY,CAACC,EAAE,EAAED,YAAY;UACtDvC,SAAS,CAACsE,OAAO,CAAC,MAAM;QAC1B,CAAC,MAAM;UACL,MAAM7D,WAAW,CAACiE,MAAM,CAACnC,YAAY;UACrCvC,SAAS,CAACsE,OAAO,CAAC,MAAM;QAC1B;QAEApD,aAAa,CAACmC,KAAK,GAAG,KAAI;QAC1BE,eAAe,CAAC;MAClB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd3D,SAAS,CAAC2D,KAAK,CAACvC,MAAM,CAACiC,KAAK,GAAG,MAAM,GAAG,MAAM;MAChD,CAAC,SAAS;QACRlC,UAAU,CAACkC,KAAK,GAAG,KAAI;MACzB;IACF;;IAEA;IACA,MAAMQ,SAAS,GAAGA,CAAA,KAAM;MACtBzC,MAAM,CAACiC,KAAK,GAAG,KAAI;MACnBd,YAAY,CAACC,EAAE,GAAG,EAAC;MACnBD,YAAY,CAACF,QAAQ,GAAG,EAAC;MACzBE,YAAY,CAACD,aAAa,GAAG,EAAC;MAC9BC,YAAY,CAACE,OAAO,GAAG,EAAC;MACxB,IAAIpB,OAAO,CAACgC,KAAK,EAAE;QACjBhC,OAAO,CAACgC,KAAK,CAACsB,WAAW,CAAC;MAC5B;IACF;;IAEA;IACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxBxC,UAAU,CAACC,QAAQ,GAAG,EAAC;MACvBD,UAAU,CAACE,aAAa,GAAG,EAAC;MAC5BiB,eAAe,CAAC;IAClB;;IAEA;IACA,MAAMsB,eAAe,GAAG,MAAOxC,QAAQ,IAAK;MAC1CR,eAAe,CAACwB,KAAK,GAAGhB,QAAO;MAC/Bb,kBAAkB,CAAC6B,KAAK,GAAG,IAAG;MAC9B,MAAMyB,UAAU,CAACzC,QAAQ,CAACG,EAAE;IAC9B;;IAEA;IACA,MAAMsC,UAAU,GAAG,MAAOC,UAAU,IAAK;MACvCxD,YAAY,CAAC8B,KAAK,GAAG,IAAG;MACxB,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAM9C,OAAO,CAACsE,aAAa,CAACD,UAAU;QACvDzD,KAAK,CAAC+B,KAAK,GAAGG,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAG;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd3D,SAAS,CAAC2D,KAAK,CAAC,UAAU;MAC5B,CAAC,SAAS;QACRpC,YAAY,CAAC8B,KAAK,GAAG,KAAI;MAC3B;IACF;;IAEA;IACA,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;MAC9BtD,UAAU,CAAC0B,KAAK,GAAG,KAAI;MACvBX,QAAQ,CAACC,WAAW,GAAGd,eAAe,CAACwB,KAAK,CAACb,EAAC;MAC9Cf,iBAAiB,CAAC4B,KAAK,GAAG,IAAG;IAC/B;;IAEA;IACA,MAAM6B,QAAQ,GAAInB,GAAG,IAAK;MACxBpC,UAAU,CAAC0B,KAAK,GAAG,IAAG;MACtBX,QAAQ,CAACF,EAAE,GAAGuB,GAAG,CAACvB,EAAC;MACnBE,QAAQ,CAACC,WAAW,GAAGoB,GAAG,CAACpB,WAAU;MACrCD,QAAQ,CAACE,IAAI,GAAGmB,GAAG,CAACnB,IAAG;MACvBF,QAAQ,CAACG,MAAM,GAAGkB,GAAG,CAAClB,MAAK;MAC3BH,QAAQ,CAACD,OAAO,GAAGsB,GAAG,CAACtB,OAAM;MAC7BC,QAAQ,CAACI,UAAU,GAAGiB,GAAG,CAACjB,UAAU,IAAI,CAAC,GAAG,KAAK,GAAG,IAAG;;MAEvD;MACAb,gBAAgB,CAACoB,KAAK,GAAGzC,WAAW,CAACmD,GAAG,CAACoB,SAAS;MAClDjD,iBAAiB,CAACmB,KAAK,GAAGzC,WAAW,CAACmD,GAAG,CAACqB,WAAW;MACrDjD,gBAAgB,CAACkB,KAAK,GAAGzC,WAAW,CAACmD,GAAG,CAACsB,UAAU;MAEnD5D,iBAAiB,CAAC4B,KAAK,GAAG,IAAG;IAC/B;;IAEA;IACA,MAAMiC,UAAU,GAAG,MAAOvB,GAAG,IAAK;MAChC,IAAI;QACF,MAAM9D,YAAY,CAACgE,OAAO,CACxB,YAAYF,GAAG,CAACnB,IAAI,yBAAyB,EAC7C,MAAM,EACN;UACEsB,iBAAiB,EAAE,MAAM;UACzBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE,SAAS;UACfmB,wBAAwB,EAAE;QAC5B,CACF;;QAEA;QACA,MAAMC,eAAe,GAAGtF,SAAS,CAACuF,OAAO,CAAC;UACxCC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,SAAS;UACfC,UAAU,EAAE;QACd,CAAC;QAED,IAAI;UACF,MAAMlF,OAAO,CAAC2D,MAAM,CAACN,GAAG,CAACvB,EAAE;UAC3BxC,SAAS,CAACsE,OAAO,CAAC,MAAM;UACxBQ,UAAU,CAACjD,eAAe,CAACwB,KAAK,CAACb,EAAE;QACrC,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdkC,OAAO,CAAClC,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5B3D,SAAS,CAAC2D,KAAK,CAAC,UAAU;QAC5B,CAAC,SAAS;UACR6B,eAAe,CAACM,KAAK,CAAC;QACxB;MACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtB3D,SAAS,CAAC2D,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAMoC,qBAAqB,GAAIC,IAAI,IAAK;MACtClE,aAAa,CAACuB,KAAK,GAAG2C,IAAI,CAACC,GAAE;MAC7BhE,gBAAgB,CAACoB,KAAK,GAAG6C,GAAG,CAACC,eAAe,CAACH,IAAI,CAACC,GAAG;IACvD;IAEA,MAAMG,sBAAsB,GAAIJ,IAAI,IAAK;MACvCjE,cAAc,CAACsB,KAAK,GAAG2C,IAAI,CAACC,GAAE;MAC9B/D,iBAAiB,CAACmB,KAAK,GAAG6C,GAAG,CAACC,eAAe,CAACH,IAAI,CAACC,GAAG;IACxD;IAEA,MAAMI,qBAAqB,GAAIL,IAAI,IAAK;MACtChE,aAAa,CAACqB,KAAK,GAAG2C,IAAI,CAACC,GAAE;MAC7B9D,gBAAgB,CAACkB,KAAK,GAAG6C,GAAG,CAACC,eAAe,CAACH,IAAI,CAACC,GAAG;IACvD;;IAEA;IACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAAC1E,WAAW,CAACyB,KAAK,EAAE;MAExB,IAAI;QACF,MAAMzB,WAAW,CAACyB,KAAK,CAACmB,QAAQ,CAAC;QAEjC,IAAI,CAAC7C,UAAU,CAAC0B,KAAK,KAAK,CAACvB,aAAa,CAACuB,KAAK,IAAI,CAACtB,cAAc,CAACsB,KAAK,IAAI,CAACrB,aAAa,CAACqB,KAAK,CAAC,EAAE;UAChGrD,SAAS,CAAC2D,KAAK,CAAC,YAAY;UAC5B;QACF;QAEAjC,cAAc,CAAC2B,KAAK,GAAG,IAAG;QAE1B,MAAMkD,QAAQ,GAAG,IAAIC,QAAQ,CAAC;QAC9BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE/D,QAAQ,CAACC,WAAW;QACnD4D,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/D,QAAQ,CAACE,IAAI;QACrC2D,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/D,QAAQ,CAACG,MAAM;QACzC0D,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE/D,QAAQ,CAACD,OAAO;;QAE3C;QACA,IAAI,CAACd,UAAU,CAAC0B,KAAK,EAAE;UACrBkD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE/D,QAAQ,CAACI,UAAU;QACnD;QAEA,IAAIhB,aAAa,CAACuB,KAAK,EAAE;UACvBkD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE3E,aAAa,CAACuB,KAAK;QAClD;QACA,IAAItB,cAAc,CAACsB,KAAK,EAAE;UACxBkD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE1E,cAAc,CAACsB,KAAK;QACrD;QACA,IAAIrB,aAAa,CAACqB,KAAK,EAAE;UACvBkD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEzE,aAAa,CAACqB,KAAK;QACnD;QAEA,IAAI1B,UAAU,CAAC0B,KAAK,EAAE;UACpB,MAAM3C,OAAO,CAAC+D,MAAM,CAAC/B,QAAQ,CAACF,EAAE,EAAE+D,QAAQ;UAC1CvG,SAAS,CAACsE,OAAO,CAAC,MAAM;QAC1B,CAAC,MAAM;UACL,MAAM5D,OAAO,CAACgE,MAAM,CAAC6B,QAAQ;UAC7BvG,SAAS,CAACsE,OAAO,CAAC,MAAM;QAC1B;QAEA7C,iBAAiB,CAAC4B,KAAK,GAAG,KAAI;QAC9ByB,UAAU,CAACjD,eAAe,CAACwB,KAAK,CAACb,EAAE;MACrC,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACd3D,SAAS,CAAC2D,KAAK,CAAChC,UAAU,CAAC0B,KAAK,GAAG,MAAM,GAAG,MAAM;MACpD,CAAC,SAAS;QACR3B,cAAc,CAAC2B,KAAK,GAAG,KAAI;MAC7B;IACF;;IAEA;IACA,MAAMqD,mBAAmB,GAAG,MAAOrD,KAAK,IAAK;MAC3C;MACA,IAAI,CAAC1B,UAAU,CAAC0B,KAAK,EAAE;QACrB,IAAIA,KAAK,EAAE;UACTrD,SAAS,CAAC2G,IAAI,CAAC,2BAA2B;QAC5C;QACA;MACF;;MAEA;MACA,IAAIhF,UAAU,CAAC0B,KAAK,IAAIX,QAAQ,CAACF,EAAE,EAAE;QACnC,IAAI;UACF,MAAM9B,OAAO,CAACkG,aAAa,CAAClE,QAAQ,CAACF,EAAE,EAAEa,KAAK;UAC9CrD,SAAS,CAACsE,OAAO,CAACjB,KAAK,GAAG,SAAS,GAAG,SAAS;;UAE/C;UACA,IAAIxB,eAAe,CAACwB,KAAK,EAAE;YACzB,MAAMyB,UAAU,CAACjD,eAAe,CAACwB,KAAK,CAACb,EAAE;UAC3C;QACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdkC,OAAO,CAAClC,KAAK,CAAC,WAAW,EAAEA,KAAK;UAChC3D,SAAS,CAAC2D,KAAK,CAAC,UAAU;UAC1B;UACAjB,QAAQ,CAACI,UAAU,GAAG,CAACO,KAAI;QAC7B;MACF;IACF;;IAEA;IACA,MAAMwD,aAAa,GAAGA,CAAA,KAAM;MAC1BlF,UAAU,CAAC0B,KAAK,GAAG,KAAI;MACvBX,QAAQ,CAACF,EAAE,GAAG,EAAC;MACfE,QAAQ,CAACC,WAAW,GAAG,EAAC;MACxBD,QAAQ,CAACE,IAAI,GAAG,EAAC;MACjBF,QAAQ,CAACG,MAAM,GAAG,QAAO;MACzBH,QAAQ,CAACD,OAAO,GAAG,EAAC;MACpBC,QAAQ,CAACI,UAAU,GAAG,KAAI;;MAE1B;MACAhB,aAAa,CAACuB,KAAK,GAAG,IAAG;MACzBtB,cAAc,CAACsB,KAAK,GAAG,IAAG;MAC1BrB,aAAa,CAACqB,KAAK,GAAG,IAAG;MACzBpB,gBAAgB,CAACoB,KAAK,GAAG,EAAC;MAC1BnB,iBAAiB,CAACmB,KAAK,GAAG,EAAC;MAC3BlB,gBAAgB,CAACkB,KAAK,GAAG,EAAC;MAE1B,IAAIzB,WAAW,CAACyB,KAAK,EAAE;QACrBzB,WAAW,CAACyB,KAAK,CAACsB,WAAW,CAAC;MAChC;IACF;;IAEA;IACA,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;MAC7BxF,KAAK,CAAC+B,KAAK,GAAG,EAAC;MACfxB,eAAe,CAACwB,KAAK,GAAG,IAAG;IAC7B;;IAEA;IACA,MAAM0D,UAAU,GAAIC,IAAI,IAAK;MAC3B,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC;IACvC;;IAEA;IACA,MAAMC,mBAAmB,GAAIC,YAAY,IAAK;MAC5C,MAAMC,OAAO,GAAG;QACd,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;MACR;MACA,OAAOA,OAAO,CAACD,YAAY,CAAC,IAAI,SAAQ;IAC1C;;IAEA;IACA,MAAME,YAAY,GAAIC,GAAG,IAAK;MAC5B;MACA1B,OAAO,CAAC2B,GAAG,CAAC,OAAO,EAAED,GAAG;IAC1B;;IAEA;IACAzH,SAAS,CAAC,MAAM;MACdyD,eAAe,CAAC;IAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}