<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .category-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .category-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .item-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 10px; }
        .item { text-align: center; padding: 10px; border: 1px solid #eee; border-radius: 3px; }
        .item img { width: 50px; height: 50px; object-fit: cover; }
        .loading { color: #666; font-style: italic; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>乐高分类API测试</h1>
    
    <div id="status"></div>
    
    <div class="category-section">
        <h2>测试按钮</h2>
        <button onclick="testAPI('头部')">测试头部数据</button>
        <button onclick="testAPI('发型')">测试发型数据</button>
        <button onclick="testAPI('躯干')">测试躯干数据</button>
        <button onclick="testAPI('腿部')">测试腿部数据</button>
        <button onclick="testAPI('配件')">测试配件数据</button>
        <button onclick="testAllCategories()">测试所有分类</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:3000/api';
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function displayResults(title, data) {
            const resultsDiv = document.getElementById('results');
            let html = `<div class="category-section">
                <div class="category-title">${title}</div>`;
            
            if (data && data.length > 0) {
                html += '<div class="item-grid">';
                data.forEach(item => {
                    html += `<div class="item">
                        <img src="${item.src || 'https://via.placeholder.com/50'}" alt="${item.alt || item.name}" onerror="this.src='https://via.placeholder.com/50'">
                        <div>${item.alt || item.name}</div>
                    </div>`;
                });
                html += '</div>';
            } else {
                html += '<div>暂无数据</div>';
            }
            
            html += '</div>';
            resultsDiv.innerHTML += html;
        }
        
        async function testAPI(mainCategory) {
            setStatus(`正在测试${mainCategory}数据...`, 'loading');
            document.getElementById('results').innerHTML = '';
            
            try {
                // 1. 获取该大类下的分类
                const categoriesResponse = await axios.get(`${API_BASE_URL}/categories`, {
                    params: { main_category: mainCategory }
                });
                
                console.log(`${mainCategory}分类响应:`, categoriesResponse.data);
                
                const categories = categoriesResponse.data.data || [];
                
                if (categories.length === 0) {
                    setStatus(`${mainCategory}暂无分类数据`, 'error');
                    return;
                }
                
                // 2. 获取每个分类的子项
                for (const category of categories) {
                    try {
                        const itemsResponse = await axios.get(`${API_BASE_URL}/items/category/${category.id}`);
                        const items = itemsResponse.data.data || [];
                        
                        console.log(`分类${category.category}的子项:`, items);
                        
                        // 转换数据格式用于显示
                        const displayItems = items.map(item => ({
                            id: item.id,
                            src: getImageUrl(item.thumbnail),
                            alt: item.name,
                            name: item.name
                        }));
                        
                        displayResults(`${category.category} (${items.length}个子项)`, displayItems);
                        
                    } catch (error) {
                        console.error(`获取分类${category.id}的子项失败:`, error);
                        displayResults(`${category.category} (获取失败)`, []);
                    }
                }
                
                setStatus(`${mainCategory}数据加载完成`, 'success');
                
            } catch (error) {
                console.error(`测试${mainCategory}失败:`, error);
                setStatus(`测试${mainCategory}失败: ${error.message}`, 'error');
            }
        }
        
        async function testAllCategories() {
            setStatus('正在测试所有分类...', 'loading');
            document.getElementById('results').innerHTML = '';
            
            try {
                const response = await axios.get(`${API_BASE_URL}/categories`);
                const categories = response.data.data || [];
                
                console.log('所有分类:', categories);
                
                // 按大类分组显示
                const groupedCategories = {};
                categories.forEach(category => {
                    if (!groupedCategories[category.main_category]) {
                        groupedCategories[category.main_category] = [];
                    }
                    groupedCategories[category.main_category].push(category);
                });
                
                for (const [mainCategory, categoryList] of Object.entries(groupedCategories)) {
                    displayResults(`${mainCategory} (${categoryList.length}个分类)`, 
                        categoryList.map(cat => ({
                            name: cat.category,
                            alt: cat.category
                        }))
                    );
                }
                
                setStatus(`所有分类数据加载完成，共${categories.length}个分类`, 'success');
                
            } catch (error) {
                console.error('测试所有分类失败:', error);
                setStatus(`测试失败: ${error.message}`, 'error');
            }
        }
        
        function getImageUrl(imagePath) {
            if (!imagePath) return 'https://via.placeholder.com/50';
            if (imagePath.startsWith('http')) return imagePath;
            return `http://127.0.0.1:3000/${imagePath.replace(/^\/+/, '')}`;
        }
        
        // 页面加载完成后自动测试连接
        window.onload = function() {
            setStatus('页面加载完成，点击按钮测试API', 'success');
        };
    </script>
</body>
</html>
