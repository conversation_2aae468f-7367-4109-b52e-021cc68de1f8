const Item = require('../models/itemModel');
const Category = require('../models/categoryModel');
const fs = require('fs');
const path = require('path');

// 获取所有子元素
exports.getAllItems = async (req, res) => {
  try {
    const items = await Item.findAll();
    res.status(200).json({
      success: true,
      count: items.length,
      data: items
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取子元素列表失败',
      error: error.message
    });
  }
};

// 根据分类获取子元素
exports.getItemsByCategory = async (req, res) => {
  try {
    const categoryId = req.params.categoryId;
    
    // 验证分类是否存在
    const category = await Category.findById(categoryId);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '未找到该分类'
      });
    }
    
    const items = await Item.findByCategoryId(categoryId);
    
    res.status(200).json({
      success: true,
      count: items.length,
      data: items
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取分类下子元素失败',
      error: error.message
    });
  }
};

// 获取单个子元素
exports.getItemById = async (req, res) => {
  try {
    const item = await Item.findById(req.params.id);
    
    if (!item) {
      return res.status(404).json({
        success: false,
        message: '未找到该子元素'
      });
    }
    
    res.status(200).json({
      success: true,
      data: item
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取子元素信息失败',
      error: error.message
    });
  }
};

// 创建子元素
exports.createItem = async (req, res) => {
  try {
    const { category_id, name, status, content, is_default } = req.body;
    
    // 验证必填字段
    if (!category_id) {
      return res.status(400).json({
        success: false,
        message: '请提供分类ID'
      });
    }
    
    if (!req.files || !req.files.thumbnail || !req.files.front_image || !req.files.back_image) {
      return res.status(400).json({
        success: false,
        message: '请上传缩略图、前置图和后置图'
      });
    }
    
    // 验证分类是否存在
    const category = await Category.findById(category_id);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '未找到该分类'
      });
    }
    
    const itemData = {
      category_id,
      name,
      thumbnail: req.files.thumbnail[0].path,
      front_image: req.files.front_image[0].path,
      back_image: req.files.back_image[0].path,
      status,
      content,
      is_default: false // 先创建为非默认，后续单独处理默认逻辑
    };

    const itemId = await Item.create(itemData);

    // 如果设置为默认，则调用专门的方法处理
    if (is_default === 'true' || is_default === true) {
      await Item.setAsDefault(itemId);
    }

    const newItem = await Item.findById(itemId);
    
    res.status(201).json({
      success: true,
      message: '子元素创建成功',
      data: newItem
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建子元素失败',
      error: error.message
    });
  }
};

// 更新子元素
exports.updateItem = async (req, res) => {
  try {
    const item = await Item.findById(req.params.id);
    
    if (!item) {
      return res.status(404).json({
        success: false,
        message: '未找到该子元素'
      });
    }
    
    // 处理图片更新
    let thumbnail = item.thumbnail;
    let front_image = item.front_image;
    let back_image = item.back_image;
    
    if (req.files) {
      if (req.files.thumbnail) {
        // 删除旧的缩略图
        if (item.thumbnail && fs.existsSync(item.thumbnail)) {
          fs.unlinkSync(item.thumbnail);
        }
        thumbnail = req.files.thumbnail[0].path;
      }
      
      if (req.files.front_image) {
        // 删除旧的前置图
        if (item.front_image && fs.existsSync(item.front_image)) {
          fs.unlinkSync(item.front_image);
        }
        front_image = req.files.front_image[0].path;
      }
      
      if (req.files.back_image) {
        // 删除旧的后置图
        if (item.back_image && fs.existsSync(item.back_image)) {
          fs.unlinkSync(item.back_image);
        }
        back_image = req.files.back_image[0].path;
      }
    }
    
    const itemData = {
      category_id: req.body.category_id || item.category_id,
      name: req.body.name || item.name,
      thumbnail,
      front_image,
      back_image,
      status: req.body.status || item.status,
      content: req.body.content || item.content,
      is_default: item.is_default // 保持原有的默认状态，通过专门的API来修改
    };

    const updated = await Item.update(req.params.id, itemData);

    if (updated) {
      const updatedItem = await Item.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '子元素更新成功',
        data: updatedItem
      });
    } else {
      res.status(500).json({
        success: false,
        message: '子元素更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新子元素失败',
      error: error.message
    });
  }
};

// 删除子元素
exports.deleteItem = async (req, res) => {
  try {
    const item = await Item.findById(req.params.id);
    
    if (!item) {
      return res.status(404).json({
        success: false,
        message: '未找到该子元素'
      });
    }
    
    // 删除关联的图片文件
    if (item.thumbnail && fs.existsSync(item.thumbnail)) {
      fs.unlinkSync(item.thumbnail);
    }
    if (item.front_image && fs.existsSync(item.front_image)) {
      fs.unlinkSync(item.front_image);
    }
    if (item.back_image && fs.existsSync(item.back_image)) {
      fs.unlinkSync(item.back_image);
    }
    
    const deleted = await Item.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '子元素删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '子元素删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除子元素失败',
      error: error.message
    });
  }
};

// 获取默认配置项（5个部件的默认项）
exports.getDefaultItems = async (req, res) => {
  try {
    const defaultItems = await Item.findDefaultItems();

    // 按大类分组
    const groupedDefaults = {
      头部: null,
      发型: null,
      服装: null,
      装饰: null,
      腿部: null,
      配件: null,
      背景: null,
      道具: null
    };

    defaultItems.forEach(item => {
      if (item.main_category && groupedDefaults.hasOwnProperty(item.main_category)) {
        groupedDefaults[item.main_category] = {
          id: item.id,
          name: item.name,
          src: 'http://127.0.0.1:3000/' + item.thumbnail,
          src2: 'http://127.0.0.1:3000/' + item.front_image,
          src3: 'http://127.0.0.1:3000/' + item.back_image,
          alt: item.name,
          dataClass: item.status,
          category_name: item.category_name,
          main_category: item.main_category
        };
      }
    });

    res.status(200).json({
      success: true,
      message: '获取默认配置成功',
      data: groupedDefaults
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取默认配置失败',
      error: error.message
    });
  }
};

// 设置/取消默认状态
exports.toggleDefault = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_default } = req.body;

    // 验证参数
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：id'
      });
    }

    // 检查项目是否存在
    const item = await Item.findById(id);
    if (!item) {
      return res.status(404).json({
        success: false,
        message: '未找到该子元素'
      });
    }

    if (is_default === true || is_default === 'true') {
      // 设置为默认
      await Item.setAsDefault(id);
      res.status(200).json({
        success: true,
        message: '已设置为默认项'
      });
    } else {
      // 取消默认
      await Item.unsetAsDefault(id);
      res.status(200).json({
        success: true,
        message: '已取消默认状态'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '操作失败',
      error: error.message
    });
  }
};