const express = require('express');
const router = express.Router();
const itemController = require('../controllers/itemController');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = 'uploads/items';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/items');
  },
  filename: (req, file, cb) => {
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const nameWithoutExt = path.basename(file.originalname, ext);
    cb(null, `${timestamp}-${nameWithoutExt}${ext}`);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedFileTypes = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
  const extname = path.extname(file.originalname).toLowerCase();
  
  if (allowedFileTypes.includes(extname)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型。允许的类型: JPG, JPEG, PNG, GIF, WEBP'), false);
  }
};

const upload = multer({ 
  storage,
  fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 限制文件大小为10MB
});

// 获取所有子元素
router.get('/', itemController.getAllItems);

// 获取默认配置项（5个部件的默认项）
router.get('/defaults', itemController.getDefaultItems);

// 根据分类ID获取子元素
router.get('/category/:categoryId', itemController.getItemsByCategory);

// 获取单个子元素信息
router.get('/:id', itemController.getItemById);

// 创建子元素（支持多图片上传）
router.post('/', upload.fields([
  { name: 'thumbnail', maxCount: 1 },
  { name: 'front_image', maxCount: 1 },
  { name: 'back_image', maxCount: 1 }
]), itemController.createItem);

// 更新子元素信息
router.put('/:id', upload.fields([
  { name: 'thumbnail', maxCount: 1 },
  { name: 'front_image', maxCount: 1 },
  { name: 'back_image', maxCount: 1 }
]), itemController.updateItem);

// 删除子元素
router.delete('/:id', itemController.deleteItem);

// 设置/取消默认状态
router.patch('/:id/default', itemController.toggleDefault);

module.exports = router;