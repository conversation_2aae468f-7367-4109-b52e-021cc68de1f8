{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox, ElLoading } from 'element-plus';\nimport { Plus, Search, Edit, Delete, Grid } from '@element-plus/icons-vue';\nimport { categoryAPI, itemAPI } from '@/utils/api';\n\n// 使用环境变量获取\nconst API_BASE_URL = 'http://localhost:3000';\n\n// 创建图片URL的辅助函数\n\nexport default {\n  __name: 'CategoryList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const getImageUrl = imagePath => {\n      if (!imagePath) return '';\n      // 如果路径已经包含http，直接返回\n      if (imagePath.startsWith('http')) return imagePath;\n      // 否则拼接基础URL\n      return `${API_BASE_URL}/${imagePath.replace(/^\\/+/, '')}`;\n    };\n\n    // 响应式数据\n    const categories = ref([]);\n    const loading = ref(false);\n    const dialogVisible = ref(false);\n    const submitting = ref(false);\n    const isEdit = ref(false);\n    const formRef = ref();\n\n    // 子项相关数据\n    const items = ref([]);\n    const itemsLoading = ref(false);\n    const itemsDialogVisible = ref(false);\n    const itemDialogVisible = ref(false);\n    const itemSubmitting = ref(false);\n    const isItemEdit = ref(false);\n    const itemFormRef = ref();\n    const currentCategory = ref(null);\n\n    // 图片相关\n    const thumbnailFile = ref(null);\n    const frontImageFile = ref(null);\n    const backImageFile = ref(null);\n    const thumbnailPreview = ref('');\n    const frontImagePreview = ref('');\n    const backImagePreview = ref('');\n\n    // 搜索表单\n    const searchForm = reactive({\n      category: ''\n    });\n\n    // 分类表单\n    const categoryForm = reactive({\n      id: '',\n      category: '',\n      content: ''\n    });\n\n    // 子项表单\n    const itemForm = reactive({\n      id: '',\n      category_id: '',\n      name: '',\n      status: 'active',\n      content: ''\n    });\n\n    // 表单验证规则\n    const rules = {\n      category: [{\n        required: true,\n        message: '请输入分类名称',\n        trigger: 'blur'\n      }]\n    };\n    const itemRules = {\n      name: [{\n        required: true,\n        message: '请输入名称',\n        trigger: 'blur'\n      }]\n    };\n\n    // 计算属性\n    const dialogTitle = computed(() => isEdit.value ? '编辑分类' : '添加分类');\n    const itemDialogTitle = computed(() => isItemEdit.value ? '编辑子项' : '添加子项');\n\n    // 获取分类列表\n    const fetchCategories = async () => {\n      loading.value = true;\n      try {\n        const response = await categoryAPI.getAll(searchForm);\n        categories.value = response.data.data || response.data;\n      } catch (error) {\n        ElMessage.error('获取分类列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 显示添加对话框\n    const showAddDialog = () => {\n      isEdit.value = false;\n      dialogVisible.value = true;\n    };\n\n    // 编辑分类\n    const editCategory = row => {\n      isEdit.value = true;\n      categoryForm.id = row.id;\n      categoryForm.category = row.category;\n      categoryForm.content = row.content;\n      dialogVisible.value = true;\n    };\n\n    // 删除分类\n    const deleteCategory = async row => {\n      try {\n        await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        await categoryAPI.delete(row.id);\n        ElMessage.success('删除成功');\n        fetchCategories();\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error('删除失败');\n        }\n      }\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!formRef.value) return;\n      try {\n        await formRef.value.validate();\n        submitting.value = true;\n        if (isEdit.value) {\n          await categoryAPI.update(categoryForm.id, categoryForm);\n          ElMessage.success('更新成功');\n        } else {\n          await categoryAPI.create(categoryForm);\n          ElMessage.success('创建成功');\n        }\n        dialogVisible.value = false;\n        fetchCategories();\n      } catch (error) {\n        ElMessage.error(isEdit.value ? '更新失败' : '创建失败');\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      categoryForm.id = '';\n      categoryForm.category = '';\n      categoryForm.content = '';\n      if (formRef.value) {\n        formRef.value.resetFields();\n      }\n    };\n\n    // 重置搜索\n    const resetSearch = () => {\n      searchForm.category = '';\n      fetchCategories();\n    };\n\n    // 显示子项管理对话框\n    const showItemsDialog = async category => {\n      currentCategory.value = category;\n      itemsDialogVisible.value = true;\n      await fetchItems(category.id);\n    };\n\n    // 获取子项列表\n    const fetchItems = async categoryId => {\n      itemsLoading.value = true;\n      try {\n        const response = await itemAPI.getByCategory(categoryId);\n        items.value = response.data.data || response.data;\n      } catch (error) {\n        ElMessage.error('获取子项列表失败');\n      } finally {\n        itemsLoading.value = false;\n      }\n    };\n\n    // 显示添加子项对话框\n    const showAddItemDialog = () => {\n      isItemEdit.value = false;\n      itemForm.category_id = currentCategory.value.id;\n      itemDialogVisible.value = true;\n    };\n\n    // 编辑子项 - 使用辅助函数\n    const editItem = row => {\n      isItemEdit.value = true;\n      itemForm.id = row.id;\n      itemForm.category_id = row.category_id;\n      itemForm.name = row.name;\n      itemForm.status = row.status;\n      itemForm.content = row.content;\n\n      // 图片回显 - 使用辅助函数\n      thumbnailPreview.value = getImageUrl(row.thumbnail);\n      frontImagePreview.value = getImageUrl(row.front_image);\n      backImagePreview.value = getImageUrl(row.back_image);\n      itemDialogVisible.value = true;\n    };\n\n    // 删除子项 - 添加二次确认和图片删除\n    const deleteItem = async row => {\n      try {\n        await ElMessageBox.confirm(`确定要删除子项 \"${row.name}\" 吗？删除后将无法恢复，相关图片也会被删除。`, '删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: true\n        });\n\n        // 显示删除进度\n        const loadingInstance = ElLoading.service({\n          lock: true,\n          text: '正在删除...',\n          background: 'rgba(0, 0, 0, 0.7)'\n        });\n        try {\n          await itemAPI.delete(row.id);\n          ElMessage.success('删除成功');\n          fetchItems(currentCategory.value.id);\n        } catch (error) {\n          console.error('删除失败:', error);\n          ElMessage.error('删除失败，请重试');\n        } finally {\n          loadingInstance.close();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error('删除操作失败');\n        }\n      }\n    };\n\n    // 处理图片上传\n    const handleThumbnailChange = file => {\n      thumbnailFile.value = file.raw;\n      thumbnailPreview.value = URL.createObjectURL(file.raw);\n    };\n    const handleFrontImageChange = file => {\n      frontImageFile.value = file.raw;\n      frontImagePreview.value = URL.createObjectURL(file.raw);\n    };\n    const handleBackImageChange = file => {\n      backImageFile.value = file.raw;\n      backImagePreview.value = URL.createObjectURL(file.raw);\n    };\n\n    // 提交子项表单\n    const submitItemForm = async () => {\n      if (!itemFormRef.value) return;\n      try {\n        await itemFormRef.value.validate();\n        if (!isItemEdit.value && (!thumbnailFile.value || !frontImageFile.value || !backImageFile.value)) {\n          ElMessage.error('请上传所有必需的图片');\n          return;\n        }\n        itemSubmitting.value = true;\n        const formData = new FormData();\n        formData.append('category_id', itemForm.category_id);\n        formData.append('name', itemForm.name);\n        formData.append('status', itemForm.status);\n        formData.append('content', itemForm.content);\n        if (thumbnailFile.value) {\n          formData.append('thumbnail', thumbnailFile.value);\n        }\n        if (frontImageFile.value) {\n          formData.append('front_image', frontImageFile.value);\n        }\n        if (backImageFile.value) {\n          formData.append('back_image', backImageFile.value);\n        }\n        if (isItemEdit.value) {\n          await itemAPI.update(itemForm.id, formData);\n          ElMessage.success('更新成功');\n        } else {\n          await itemAPI.create(formData);\n          ElMessage.success('创建成功');\n        }\n        itemDialogVisible.value = false;\n        fetchItems(currentCategory.value.id);\n      } catch (error) {\n        ElMessage.error(isItemEdit.value ? '更新失败' : '创建失败');\n      } finally {\n        itemSubmitting.value = false;\n      }\n    };\n\n    // 重置子项表单 - 清空图片预览\n    const resetItemForm = () => {\n      itemForm.id = '';\n      itemForm.category_id = '';\n      itemForm.name = '';\n      itemForm.status = 'active';\n      itemForm.content = '';\n\n      // 清空图片文件和预览\n      thumbnailFile.value = null;\n      frontImageFile.value = null;\n      backImageFile.value = null;\n      thumbnailPreview.value = '';\n      frontImagePreview.value = '';\n      backImagePreview.value = '';\n      if (itemFormRef.value) {\n        itemFormRef.value.resetFields();\n      }\n    };\n\n    // 重置子项对话框\n    const resetItemsDialog = () => {\n      items.value = [];\n      currentCategory.value = null;\n    };\n\n    // 格式化日期\n    const formatDate = date => {\n      return new Date(date).toLocaleString();\n    };\n\n    // 图片预览功能\n    const previewImage = src => {\n      // Element Plus 的图片预览会自动处理\n      console.log('预览图片:', src);\n    };\n\n    // 页面加载时获取数据\n    onMounted(() => {\n      fetchCategories();\n    });\n    const __returned__ = {\n      API_BASE_URL,\n      getImageUrl,\n      categories,\n      loading,\n      dialogVisible,\n      submitting,\n      isEdit,\n      formRef,\n      items,\n      itemsLoading,\n      itemsDialogVisible,\n      itemDialogVisible,\n      itemSubmitting,\n      isItemEdit,\n      itemFormRef,\n      currentCategory,\n      thumbnailFile,\n      frontImageFile,\n      backImageFile,\n      thumbnailPreview,\n      frontImagePreview,\n      backImagePreview,\n      searchForm,\n      categoryForm,\n      itemForm,\n      rules,\n      itemRules,\n      dialogTitle,\n      itemDialogTitle,\n      fetchCategories,\n      showAddDialog,\n      editCategory,\n      deleteCategory,\n      submitForm,\n      resetForm,\n      resetSearch,\n      showItemsDialog,\n      fetchItems,\n      showAddItemDialog,\n      editItem,\n      deleteItem,\n      handleThumbnailChange,\n      handleFrontImageChange,\n      handleBackImageChange,\n      submitItemForm,\n      resetItemForm,\n      resetItemsDialog,\n      formatDate,\n      previewImage,\n      ref,\n      reactive,\n      onMounted,\n      computed,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get ElLoading() {\n        return ElLoading;\n      },\n      get Plus() {\n        return Plus;\n      },\n      get Search() {\n        return Search;\n      },\n      get Edit() {\n        return Edit;\n      },\n      get Delete() {\n        return Delete;\n      },\n      get Grid() {\n        return Grid;\n      },\n      get categoryAPI() {\n        return categoryAPI;\n      },\n      get itemAPI() {\n        return itemAPI;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "ElMessage", "ElMessageBox", "ElLoading", "Plus", "Search", "Edit", "Delete", "Grid", "categoryAPI", "itemAPI", "API_BASE_URL", "getImageUrl", "imagePath", "startsWith", "replace", "categories", "loading", "dialogVisible", "submitting", "isEdit", "formRef", "items", "itemsLoading", "itemsDialogVisible", "itemDialogVisible", "itemSubmitting", "isItemEdit", "itemFormRef", "currentCategory", "thumbnailFile", "frontImageFile", "backImageFile", "thumbnailPreview", "frontImagePreview", "backImagePreview", "searchForm", "category", "categoryForm", "id", "content", "itemForm", "category_id", "name", "status", "rules", "required", "message", "trigger", "itemRules", "dialogTitle", "value", "itemDialogTitle", "fetchCategories", "response", "getAll", "data", "error", "showAddDialog", "editCategory", "row", "deleteCategory", "confirm", "confirmButtonText", "cancelButtonText", "type", "delete", "success", "submitForm", "validate", "update", "create", "resetForm", "resetFields", "resetSearch", "showItemsDialog", "fetchItems", "categoryId", "getByCategory", "showAddItemDialog", "editItem", "thumbnail", "front_image", "back_image", "deleteItem", "dangerouslyUseHTMLString", "loadingInstance", "service", "lock", "text", "background", "console", "close", "handleThumbnailChange", "file", "raw", "URL", "createObjectURL", "handleFrontImageChange", "handleBackImageChange", "submitItemForm", "formData", "FormData", "append", "resetItemForm", "resetItemsDialog", "formatDate", "date", "Date", "toLocaleString", "previewImage", "src", "log"], "sources": ["D:/admin/202506/乐高/乐高后台/后台管理系统v2/后台管理系统/ms/src/views/categories/CategoryList.vue"], "sourcesContent": ["<template>\n  <div class=\"category-list\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>分类管理</h2>\n      <el-button type=\"primary\" @click=\"showAddDialog\">\n        <el-icon><Plus /></el-icon>\n        添加分类\n      </el-button>\n    </div>\n\n    <!-- 搜索区域 -->\n    <div class=\"search-area\">\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n        <el-form-item label=\"分类名称\">\n          <el-input v-model=\"searchForm.category\" placeholder=\"请输入分类名称\" clearable style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item label=\"大类\">\n          <el-select v-model=\"searchForm.main_category\" placeholder=\"请选择大类\" clearable style=\"width: 150px\">\n            <el-option label=\"头部\" value=\"头部\" />\n            <el-option label=\"发型\" value=\"发型\" />\n            <el-option label=\"躯干\" value=\"躯干\" />\n            <el-option label=\"腿部\" value=\"腿部\" />\n            <el-option label=\"配件\" value=\"配件\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"fetchCategories\">\n            <el-icon><Search /></el-icon>\n            搜索\n          </el-button>\n          <el-button @click=\"resetSearch\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <!-- 表格区域 -->\n    <div class=\"table-container\">\n      <el-table :data=\"categories\" v-loading=\"loading\" stripe border>\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column prop=\"category\" label=\"分类名称\" min-width=\"150\" />\n        <el-table-column prop=\"content\" label=\"内容描述\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"180\">\n          <template #default=\"{ row }\">\n            {{ formatDate(row.created_at) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"子项\" width=\"120\">\n          <template #default=\"{ row }\">\n            <el-button type=\"info\" size=\"small\" @click=\"showItemsDialog(row)\">\n              <el-icon><Grid /></el-icon>\n              管理子项\n            </el-button>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\n          <template #default=\"{ row }\">\n            <el-button type=\"primary\" size=\"small\" @click=\"editCategory(row)\">\n              <el-icon><Edit /></el-icon>\n              编辑\n            </el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"deleteCategory(row)\">\n              <el-icon><Delete /></el-icon>\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 添加/编辑分类对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      v-model=\"dialogVisible\"\n      width=\"500px\"\n      @close=\"resetForm\"\n    >\n      <el-form :model=\"categoryForm\" :rules=\"rules\" ref=\"formRef\" label-width=\"100px\">\n        <el-form-item label=\"分类名称\" prop=\"category\">\n          <el-input v-model=\"categoryForm.category\" placeholder=\"请输入分类名称\" />\n        </el-form-item>\n        <el-form-item label=\"内容描述\" prop=\"content\">\n          <el-input\n            v-model=\"categoryForm.content\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入内容描述\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 子项管理对话框 -->\n    <el-dialog\n      title=\"子项管理\"\n      v-model=\"itemsDialogVisible\"\n      width=\"80%\"\n      @close=\"resetItemsDialog\"\n    >\n      <div class=\"items-header\">\n        <h3>{{ currentCategory?.category }} - 子项列表</h3>\n        <el-button type=\"primary\" @click=\"showAddItemDialog\">\n          <el-icon><Plus /></el-icon>\n          添加子项\n        </el-button>\n      </div>\n\n      <el-table :data=\"items\" v-loading=\"itemsLoading\" stripe border>\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column label=\"缩略图\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-image\n              \n              :src=\"getImageUrl(row.thumbnail)\"\n              :preview-src-list=\"[getImageUrl(row.thumbnail)]\"\n              style=\"width: 60px; height: 60px\"\n              fit=\"cover\"\n              @click=\"previewImage(getImageUrl(row.thumbnail))\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"前置图\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-image\n              :src=\"getImageUrl(row.front_image)\"\n              :preview-src-list=\"[getImageUrl(row.front_image)]\"\n              style=\"width: 60px; height: 60px\"\n              fit=\"cover\"\n              @click=\"previewImage(getImageUrl(row.front_image))\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"后置图\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-image\n            \n              :src=\"getImageUrl(row.back_image)\"\n              :preview-src-list=\"[getImageUrl(row.back_image)]\"\n              style=\"width: 60px; height: 60px\"\n              fit=\"cover\"\n              @click=\"previewImage(getImageUrl(row.back_image))\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"name\" label=\"名称\" min-width=\"150\" />\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-tag :type=\"row.status === 'active' ? 'success' : 'danger'\">\n              {{ row.status === 'active' ? '启用' : '禁用' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"180\">\n          <template #default=\"{ row }\">\n            {{ formatDate(row.created_at) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"{ row }\">\n            <el-button type=\"primary\" size=\"small\" @click=\"editItem(row)\">编辑</el-button>\n            <el-button type=\"danger\" size=\"small\" @click=\"deleteItem(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n\n    <!-- 添加/编辑子项对话框 -->\n    <el-dialog\n    append-to-body\t\n      :title=\"itemDialogTitle\"\n      v-model=\"itemDialogVisible\"\n      width=\"600px\"\n      @close=\"resetItemForm\"\n    >\n      <el-form :model=\"itemForm\" :rules=\"itemRules\" ref=\"itemFormRef\" label-width=\"100px\">\n        <el-form-item label=\"名称\" prop=\"name\">\n          <el-input v-model=\"itemForm.name\" placeholder=\"请输入名称\" />\n        </el-form-item>\n        <el-form-item label=\"缩略图\" prop=\"thumbnail\">\n          <el-upload\n            :auto-upload=\"false\"\n            :on-change=\"handleThumbnailChange\"\n            :show-file-list=\"false\"\n            accept=\"image/*\"\n          >\n            <el-button type=\"primary\">选择缩略图</el-button>\n          </el-upload>\n          <div v-if=\"thumbnailPreview\" class=\"image-preview\">\n            <el-image :src=\"thumbnailPreview\" style=\"width: 100px; height: 100px\" fit=\"cover\" />\n          </div>\n        </el-form-item>\n        <el-form-item label=\"前置图\" prop=\"front_image\">\n          <el-upload\n            :auto-upload=\"false\"\n            :on-change=\"handleFrontImageChange\"\n            :show-file-list=\"false\"\n            accept=\"image/*\"\n          >\n            <el-button type=\"primary\">选择前置图</el-button>\n          </el-upload>\n          <div v-if=\"frontImagePreview\" class=\"image-preview\">\n            <el-image :src=\"frontImagePreview\" style=\"width: 100px; height: 100px\" fit=\"cover\" />\n          </div>\n        </el-form-item>\n        <el-form-item label=\"后置图\" prop=\"back_image\">\n          <el-upload\n            :auto-upload=\"false\"\n            :on-change=\"handleBackImageChange\"\n            :show-file-list=\"false\"\n            accept=\"image/*\"\n          >\n            <el-button type=\"primary\">选择后置图</el-button>\n          </el-upload>\n          <div v-if=\"backImagePreview\" class=\"image-preview\">\n            <el-image :src=\"backImagePreview\" style=\"width: 100px; height: 100px\" fit=\"cover\" />\n          </div>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"itemForm.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"inactive\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"内容描述\" prop=\"content\">\n          <el-input\n            v-model=\"itemForm.content\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入内容描述\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <el-button @click=\"itemDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitItemForm\" :loading=\"itemSubmitting\">确定</el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox, ElLoading } from 'element-plus'\nimport { Plus, Search, Edit, Delete, Grid } from '@element-plus/icons-vue'\nimport { categoryAPI, itemAPI } from '@/utils/api'\n\n// 使用环境变量获取\nconst API_BASE_URL = 'http://localhost:3000'\n\n// 创建图片URL的辅助函数\nconst getImageUrl = (imagePath) => {\n  if (!imagePath) return ''\n  // 如果路径已经包含http，直接返回\n  if (imagePath.startsWith('http')) return imagePath\n  // 否则拼接基础URL\n  return `${API_BASE_URL}/${imagePath.replace(/^\\/+/, '')}`\n}\n\n// 响应式数据\nconst categories = ref([])\nconst loading = ref(false)\nconst dialogVisible = ref(false)\nconst submitting = ref(false)\nconst isEdit = ref(false)\nconst formRef = ref()\n\n// 子项相关数据\nconst items = ref([])\nconst itemsLoading = ref(false)\nconst itemsDialogVisible = ref(false)\nconst itemDialogVisible = ref(false)\nconst itemSubmitting = ref(false)\nconst isItemEdit = ref(false)\nconst itemFormRef = ref()\nconst currentCategory = ref(null)\n\n// 图片相关\nconst thumbnailFile = ref(null)\nconst frontImageFile = ref(null)\nconst backImageFile = ref(null)\nconst thumbnailPreview = ref('')\nconst frontImagePreview = ref('')\nconst backImagePreview = ref('')\n\n// 搜索表单\nconst searchForm = reactive({\n  category: ''\n})\n\n// 分类表单\nconst categoryForm = reactive({\n  id: '',\n  category: '',\n  content: ''\n})\n\n// 子项表单\nconst itemForm = reactive({\n  id: '',\n  category_id: '',\n  name: '',\n  status: 'active',\n  content: ''\n})\n\n// 表单验证规则\nconst rules = {\n  category: [\n    { required: true, message: '请输入分类名称', trigger: 'blur' }\n  ]\n}\n\nconst itemRules = {\n  name: [\n    { required: true, message: '请输入名称', trigger: 'blur' }\n  ]\n}\n\n// 计算属性\nconst dialogTitle = computed(() => isEdit.value ? '编辑分类' : '添加分类')\nconst itemDialogTitle = computed(() => isItemEdit.value ? '编辑子项' : '添加子项')\n\n// 获取分类列表\nconst fetchCategories = async () => {\n  loading.value = true\n  try {\n    const response = await categoryAPI.getAll(searchForm)\n    categories.value = response.data.data || response.data\n  } catch (error) {\n    ElMessage.error('获取分类列表失败')\n  } finally {\n    loading.value = false\n  }\n}\n\n// 显示添加对话框\nconst showAddDialog = () => {\n  isEdit.value = false\n  dialogVisible.value = true\n}\n\n// 编辑分类\nconst editCategory = (row) => {\n  isEdit.value = true\n  categoryForm.id = row.id\n  categoryForm.category = row.category\n  categoryForm.content = row.content\n  dialogVisible.value = true\n}\n\n// 删除分类\nconst deleteCategory = async (row) => {\n  try {\n    await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n    \n    await categoryAPI.delete(row.id)\n    ElMessage.success('删除成功')\n    fetchCategories()\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除失败')\n    }\n  }\n}\n\n// 提交表单\nconst submitForm = async () => {\n  if (!formRef.value) return\n  \n  try {\n    await formRef.value.validate()\n    submitting.value = true\n    \n    if (isEdit.value) {\n      await categoryAPI.update(categoryForm.id, categoryForm)\n      ElMessage.success('更新成功')\n    } else {\n      await categoryAPI.create(categoryForm)\n      ElMessage.success('创建成功')\n    }\n    \n    dialogVisible.value = false\n    fetchCategories()\n  } catch (error) {\n    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')\n  } finally {\n    submitting.value = false\n  }\n}\n\n// 重置表单\nconst resetForm = () => {\n  categoryForm.id = ''\n  categoryForm.category = ''\n  categoryForm.content = ''\n  if (formRef.value) {\n    formRef.value.resetFields()\n  }\n}\n\n// 重置搜索\nconst resetSearch = () => {\n  searchForm.category = ''\n  fetchCategories()\n}\n\n// 显示子项管理对话框\nconst showItemsDialog = async (category) => {\n  currentCategory.value = category\n  itemsDialogVisible.value = true\n  await fetchItems(category.id)\n}\n\n// 获取子项列表\nconst fetchItems = async (categoryId) => {\n  itemsLoading.value = true\n  try {\n    const response = await itemAPI.getByCategory(categoryId)\n    items.value = response.data.data || response.data\n  } catch (error) {\n    ElMessage.error('获取子项列表失败')\n  } finally {\n    itemsLoading.value = false\n  }\n}\n\n// 显示添加子项对话框\nconst showAddItemDialog = () => {\n  isItemEdit.value = false\n  itemForm.category_id = currentCategory.value.id\n  itemDialogVisible.value = true\n}\n\n// 编辑子项 - 使用辅助函数\nconst editItem = (row) => {\n  isItemEdit.value = true\n  itemForm.id = row.id\n  itemForm.category_id = row.category_id\n  itemForm.name = row.name\n  itemForm.status = row.status\n  itemForm.content = row.content\n  \n  // 图片回显 - 使用辅助函数\n  thumbnailPreview.value = getImageUrl(row.thumbnail)\n  frontImagePreview.value = getImageUrl(row.front_image)\n  backImagePreview.value = getImageUrl(row.back_image)\n  \n  itemDialogVisible.value = true\n}\n\n// 删除子项 - 添加二次确认和图片删除\nconst deleteItem = async (row) => {\n  try {\n    await ElMessageBox.confirm(\n      `确定要删除子项 \"${row.name}\" 吗？删除后将无法恢复，相关图片也会被删除。`, \n      '删除确认', \n      {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: true\n      }\n    )\n    \n    // 显示删除进度\n    const loadingInstance = ElLoading.service({\n      lock: true,\n      text: '正在删除...',\n      background: 'rgba(0, 0, 0, 0.7)'\n    })\n    \n    try {\n      await itemAPI.delete(row.id)\n      ElMessage.success('删除成功')\n      fetchItems(currentCategory.value.id)\n    } catch (error) {\n      console.error('删除失败:', error)\n      ElMessage.error('删除失败，请重试')\n    } finally {\n      loadingInstance.close()\n    }\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除操作失败')\n    }\n  }\n}\n\n// 处理图片上传\nconst handleThumbnailChange = (file) => {\n  thumbnailFile.value = file.raw\n  thumbnailPreview.value = URL.createObjectURL(file.raw)\n}\n\nconst handleFrontImageChange = (file) => {\n  frontImageFile.value = file.raw\n  frontImagePreview.value = URL.createObjectURL(file.raw)\n}\n\nconst handleBackImageChange = (file) => {\n  backImageFile.value = file.raw\n  backImagePreview.value = URL.createObjectURL(file.raw)\n}\n\n// 提交子项表单\nconst submitItemForm = async () => {\n  if (!itemFormRef.value) return\n  \n  try {\n    await itemFormRef.value.validate()\n    \n    if (!isItemEdit.value && (!thumbnailFile.value || !frontImageFile.value || !backImageFile.value)) {\n      ElMessage.error('请上传所有必需的图片')\n      return\n    }\n    \n    itemSubmitting.value = true\n    \n    const formData = new FormData()\n    formData.append('category_id', itemForm.category_id)\n    formData.append('name', itemForm.name)\n    formData.append('status', itemForm.status)\n    formData.append('content', itemForm.content)\n    \n    if (thumbnailFile.value) {\n      formData.append('thumbnail', thumbnailFile.value)\n    }\n    if (frontImageFile.value) {\n      formData.append('front_image', frontImageFile.value)\n    }\n    if (backImageFile.value) {\n      formData.append('back_image', backImageFile.value)\n    }\n    \n    if (isItemEdit.value) {\n      await itemAPI.update(itemForm.id, formData)\n      ElMessage.success('更新成功')\n    } else {\n      await itemAPI.create(formData)\n      ElMessage.success('创建成功')\n    }\n    \n    itemDialogVisible.value = false\n    fetchItems(currentCategory.value.id)\n  } catch (error) {\n    ElMessage.error(isItemEdit.value ? '更新失败' : '创建失败')\n  } finally {\n    itemSubmitting.value = false\n  }\n}\n\n// 重置子项表单 - 清空图片预览\nconst resetItemForm = () => {\n  itemForm.id = ''\n  itemForm.category_id = ''\n  itemForm.name = ''\n  itemForm.status = 'active'\n  itemForm.content = ''\n  \n  // 清空图片文件和预览\n  thumbnailFile.value = null\n  frontImageFile.value = null\n  backImageFile.value = null\n  thumbnailPreview.value = ''\n  frontImagePreview.value = ''\n  backImagePreview.value = ''\n  \n  if (itemFormRef.value) {\n    itemFormRef.value.resetFields()\n  }\n}\n\n// 重置子项对话框\nconst resetItemsDialog = () => {\n  items.value = []\n  currentCategory.value = null\n}\n\n// 格式化日期\nconst formatDate = (date) => {\n  return new Date(date).toLocaleString()\n}\n\n// 图片预览功能\nconst previewImage = (src) => {\n  // Element Plus 的图片预览会自动处理\n  console.log('预览图片:', src)\n}\n\n// 页面加载时获取数据\nonMounted(() => {\n  fetchCategories()\n})\n</script>\n\n<style scoped>\n.category-list {\n  padding: 24px;\n  height: 100%;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e8eaec;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n  font-size: 20px;\n  font-weight: 500;\n}\n\n.search-area {\n  background-color: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.search-form {\n  margin: 0;\n}\n\n.table-container {\n  background-color: #fff;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.items-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.items-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.image-preview-container {\n  text-align: center;\n}\n\n.image-preview {\n  margin-top: 8px;\n}\n\n.el-imag\ne {\n  cursor: pointer;\n  border-radius: 4px;\n  border: 1px solid #dcdfe6;\n}\n\n.el-image:hover {\n  border-color: #409eff;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;;AAqPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAI;AACvD,SAASC,SAAS,EAAEC,YAAY,EAAEC,SAAS,QAAQ,cAAa;AAChE,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,QAAQ,yBAAwB;AACzE,SAASC,WAAW,EAAEC,OAAO,QAAQ,aAAY;;AAEjD;AACA,MAAMC,YAAY,GAAG,uBAAsB;;AAE3C;;;;;;;;IACA,MAAMC,WAAW,GAAIC,SAAS,IAAK;MACjC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAC;MACxB;MACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAQ;MACjD;MACA,OAAO,GAAGF,YAAY,IAAIE,SAAS,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAC;IAC1D;;IAEA;IACA,MAAMC,UAAU,GAAGnB,GAAG,CAAC,EAAE;IACzB,MAAMoB,OAAO,GAAGpB,GAAG,CAAC,KAAK;IACzB,MAAMqB,aAAa,GAAGrB,GAAG,CAAC,KAAK;IAC/B,MAAMsB,UAAU,GAAGtB,GAAG,CAAC,KAAK;IAC5B,MAAMuB,MAAM,GAAGvB,GAAG,CAAC,KAAK;IACxB,MAAMwB,OAAO,GAAGxB,GAAG,CAAC;;IAEpB;IACA,MAAMyB,KAAK,GAAGzB,GAAG,CAAC,EAAE;IACpB,MAAM0B,YAAY,GAAG1B,GAAG,CAAC,KAAK;IAC9B,MAAM2B,kBAAkB,GAAG3B,GAAG,CAAC,KAAK;IACpC,MAAM4B,iBAAiB,GAAG5B,GAAG,CAAC,KAAK;IACnC,MAAM6B,cAAc,GAAG7B,GAAG,CAAC,KAAK;IAChC,MAAM8B,UAAU,GAAG9B,GAAG,CAAC,KAAK;IAC5B,MAAM+B,WAAW,GAAG/B,GAAG,CAAC;IACxB,MAAMgC,eAAe,GAAGhC,GAAG,CAAC,IAAI;;IAEhC;IACA,MAAMiC,aAAa,GAAGjC,GAAG,CAAC,IAAI;IAC9B,MAAMkC,cAAc,GAAGlC,GAAG,CAAC,IAAI;IAC/B,MAAMmC,aAAa,GAAGnC,GAAG,CAAC,IAAI;IAC9B,MAAMoC,gBAAgB,GAAGpC,GAAG,CAAC,EAAE;IAC/B,MAAMqC,iBAAiB,GAAGrC,GAAG,CAAC,EAAE;IAChC,MAAMsC,gBAAgB,GAAGtC,GAAG,CAAC,EAAE;;IAE/B;IACA,MAAMuC,UAAU,GAAGtC,QAAQ,CAAC;MAC1BuC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGxC,QAAQ,CAAC;MAC5ByC,EAAE,EAAE,EAAE;MACNF,QAAQ,EAAE,EAAE;MACZG,OAAO,EAAE;IACX,CAAC;;IAED;IACA,MAAMC,QAAQ,GAAG3C,QAAQ,CAAC;MACxByC,EAAE,EAAE,EAAE;MACNG,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,QAAQ;MAChBJ,OAAO,EAAE;IACX,CAAC;;IAED;IACA,MAAMK,KAAK,GAAG;MACZR,QAAQ,EAAE,CACR;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO;IAE1D;IAEA,MAAMC,SAAS,GAAG;MAChBN,IAAI,EAAE,CACJ;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO;IAExD;;IAEA;IACA,MAAME,WAAW,GAAGlD,QAAQ,CAAC,MAAMoB,MAAM,CAAC+B,KAAK,GAAG,MAAM,GAAG,MAAM;IACjE,MAAMC,eAAe,GAAGpD,QAAQ,CAAC,MAAM2B,UAAU,CAACwB,KAAK,GAAG,MAAM,GAAG,MAAM;;IAEzE;IACA,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClCpC,OAAO,CAACkC,KAAK,GAAG,IAAG;MACnB,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAM7C,WAAW,CAAC8C,MAAM,CAACnB,UAAU;QACpDpB,UAAU,CAACmC,KAAK,GAAGG,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAG;MACvD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdxD,SAAS,CAACwD,KAAK,CAAC,UAAU;MAC5B,CAAC,SAAS;QACRxC,OAAO,CAACkC,KAAK,GAAG,KAAI;MACtB;IACF;;IAEA;IACA,MAAMO,aAAa,GAAGA,CAAA,KAAM;MAC1BtC,MAAM,CAAC+B,KAAK,GAAG,KAAI;MACnBjC,aAAa,CAACiC,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAMQ,YAAY,GAAIC,GAAG,IAAK;MAC5BxC,MAAM,CAAC+B,KAAK,GAAG,IAAG;MAClBb,YAAY,CAACC,EAAE,GAAGqB,GAAG,CAACrB,EAAC;MACvBD,YAAY,CAACD,QAAQ,GAAGuB,GAAG,CAACvB,QAAO;MACnCC,YAAY,CAACE,OAAO,GAAGoB,GAAG,CAACpB,OAAM;MACjCtB,aAAa,CAACiC,KAAK,GAAG,IAAG;IAC3B;;IAEA;IACA,MAAMU,cAAc,GAAG,MAAOD,GAAG,IAAK;MACpC,IAAI;QACF,MAAM1D,YAAY,CAAC4D,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE;UAC9CC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CAAC;QAED,MAAMxD,WAAW,CAACyD,MAAM,CAACN,GAAG,CAACrB,EAAE;QAC/BtC,SAAS,CAACkE,OAAO,CAAC,MAAM;QACxBd,eAAe,CAAC;MAClB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtBxD,SAAS,CAACwD,KAAK,CAAC,MAAM;QACxB;MACF;IACF;;IAEA;IACA,MAAMW,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAAC/C,OAAO,CAAC8B,KAAK,EAAE;MAEpB,IAAI;QACF,MAAM9B,OAAO,CAAC8B,KAAK,CAACkB,QAAQ,CAAC;QAC7BlD,UAAU,CAACgC,KAAK,GAAG,IAAG;QAEtB,IAAI/B,MAAM,CAAC+B,KAAK,EAAE;UAChB,MAAM1C,WAAW,CAAC6D,MAAM,CAAChC,YAAY,CAACC,EAAE,EAAED,YAAY;UACtDrC,SAAS,CAACkE,OAAO,CAAC,MAAM;QAC1B,CAAC,MAAM;UACL,MAAM1D,WAAW,CAAC8D,MAAM,CAACjC,YAAY;UACrCrC,SAAS,CAACkE,OAAO,CAAC,MAAM;QAC1B;QAEAjD,aAAa,CAACiC,KAAK,GAAG,KAAI;QAC1BE,eAAe,CAAC;MAClB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdxD,SAAS,CAACwD,KAAK,CAACrC,MAAM,CAAC+B,KAAK,GAAG,MAAM,GAAG,MAAM;MAChD,CAAC,SAAS;QACRhC,UAAU,CAACgC,KAAK,GAAG,KAAI;MACzB;IACF;;IAEA;IACA,MAAMqB,SAAS,GAAGA,CAAA,KAAM;MACtBlC,YAAY,CAACC,EAAE,GAAG,EAAC;MACnBD,YAAY,CAACD,QAAQ,GAAG,EAAC;MACzBC,YAAY,CAACE,OAAO,GAAG,EAAC;MACxB,IAAInB,OAAO,CAAC8B,KAAK,EAAE;QACjB9B,OAAO,CAAC8B,KAAK,CAACsB,WAAW,CAAC;MAC5B;IACF;;IAEA;IACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxBtC,UAAU,CAACC,QAAQ,GAAG,EAAC;MACvBgB,eAAe,CAAC;IAClB;;IAEA;IACA,MAAMsB,eAAe,GAAG,MAAOtC,QAAQ,IAAK;MAC1CR,eAAe,CAACsB,KAAK,GAAGd,QAAO;MAC/Bb,kBAAkB,CAAC2B,KAAK,GAAG,IAAG;MAC9B,MAAMyB,UAAU,CAACvC,QAAQ,CAACE,EAAE;IAC9B;;IAEA;IACA,MAAMqC,UAAU,GAAG,MAAOC,UAAU,IAAK;MACvCtD,YAAY,CAAC4B,KAAK,GAAG,IAAG;MACxB,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAM5C,OAAO,CAACoE,aAAa,CAACD,UAAU;QACvDvD,KAAK,CAAC6B,KAAK,GAAGG,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAG;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdxD,SAAS,CAACwD,KAAK,CAAC,UAAU;MAC5B,CAAC,SAAS;QACRlC,YAAY,CAAC4B,KAAK,GAAG,KAAI;MAC3B;IACF;;IAEA;IACA,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;MAC9BpD,UAAU,CAACwB,KAAK,GAAG,KAAI;MACvBV,QAAQ,CAACC,WAAW,GAAGb,eAAe,CAACsB,KAAK,CAACZ,EAAC;MAC9Cd,iBAAiB,CAAC0B,KAAK,GAAG,IAAG;IAC/B;;IAEA;IACA,MAAM6B,QAAQ,GAAIpB,GAAG,IAAK;MACxBjC,UAAU,CAACwB,KAAK,GAAG,IAAG;MACtBV,QAAQ,CAACF,EAAE,GAAGqB,GAAG,CAACrB,EAAC;MACnBE,QAAQ,CAACC,WAAW,GAAGkB,GAAG,CAAClB,WAAU;MACrCD,QAAQ,CAACE,IAAI,GAAGiB,GAAG,CAACjB,IAAG;MACvBF,QAAQ,CAACG,MAAM,GAAGgB,GAAG,CAAChB,MAAK;MAC3BH,QAAQ,CAACD,OAAO,GAAGoB,GAAG,CAACpB,OAAM;;MAE7B;MACAP,gBAAgB,CAACkB,KAAK,GAAGvC,WAAW,CAACgD,GAAG,CAACqB,SAAS;MAClD/C,iBAAiB,CAACiB,KAAK,GAAGvC,WAAW,CAACgD,GAAG,CAACsB,WAAW;MACrD/C,gBAAgB,CAACgB,KAAK,GAAGvC,WAAW,CAACgD,GAAG,CAACuB,UAAU;MAEnD1D,iBAAiB,CAAC0B,KAAK,GAAG,IAAG;IAC/B;;IAEA;IACA,MAAMiC,UAAU,GAAG,MAAOxB,GAAG,IAAK;MAChC,IAAI;QACF,MAAM1D,YAAY,CAAC4D,OAAO,CACxB,YAAYF,GAAG,CAACjB,IAAI,yBAAyB,EAC7C,MAAM,EACN;UACEoB,iBAAiB,EAAE,MAAM;UACzBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE,SAAS;UACfoB,wBAAwB,EAAE;QAC5B,CACF;;QAEA;QACA,MAAMC,eAAe,GAAGnF,SAAS,CAACoF,OAAO,CAAC;UACxCC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,SAAS;UACfC,UAAU,EAAE;QACd,CAAC;QAED,IAAI;UACF,MAAMhF,OAAO,CAACwD,MAAM,CAACN,GAAG,CAACrB,EAAE;UAC3BtC,SAAS,CAACkE,OAAO,CAAC,MAAM;UACxBS,UAAU,CAAC/C,eAAe,CAACsB,KAAK,CAACZ,EAAE;QACrC,CAAC,CAAC,OAAOkB,KAAK,EAAE;UACdkC,OAAO,CAAClC,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5BxD,SAAS,CAACwD,KAAK,CAAC,UAAU;QAC5B,CAAC,SAAS;UACR6B,eAAe,CAACM,KAAK,CAAC;QACxB;MACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtBxD,SAAS,CAACwD,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAMoC,qBAAqB,GAAIC,IAAI,IAAK;MACtChE,aAAa,CAACqB,KAAK,GAAG2C,IAAI,CAACC,GAAE;MAC7B9D,gBAAgB,CAACkB,KAAK,GAAG6C,GAAG,CAACC,eAAe,CAACH,IAAI,CAACC,GAAG;IACvD;IAEA,MAAMG,sBAAsB,GAAIJ,IAAI,IAAK;MACvC/D,cAAc,CAACoB,KAAK,GAAG2C,IAAI,CAACC,GAAE;MAC9B7D,iBAAiB,CAACiB,KAAK,GAAG6C,GAAG,CAACC,eAAe,CAACH,IAAI,CAACC,GAAG;IACxD;IAEA,MAAMI,qBAAqB,GAAIL,IAAI,IAAK;MACtC9D,aAAa,CAACmB,KAAK,GAAG2C,IAAI,CAACC,GAAE;MAC7B5D,gBAAgB,CAACgB,KAAK,GAAG6C,GAAG,CAACC,eAAe,CAACH,IAAI,CAACC,GAAG;IACvD;;IAEA;IACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACxE,WAAW,CAACuB,KAAK,EAAE;MAExB,IAAI;QACF,MAAMvB,WAAW,CAACuB,KAAK,CAACkB,QAAQ,CAAC;QAEjC,IAAI,CAAC1C,UAAU,CAACwB,KAAK,KAAK,CAACrB,aAAa,CAACqB,KAAK,IAAI,CAACpB,cAAc,CAACoB,KAAK,IAAI,CAACnB,aAAa,CAACmB,KAAK,CAAC,EAAE;UAChGlD,SAAS,CAACwD,KAAK,CAAC,YAAY;UAC5B;QACF;QAEA/B,cAAc,CAACyB,KAAK,GAAG,IAAG;QAE1B,MAAMkD,QAAQ,GAAG,IAAIC,QAAQ,CAAC;QAC9BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE9D,QAAQ,CAACC,WAAW;QACnD2D,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE9D,QAAQ,CAACE,IAAI;QACrC0D,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE9D,QAAQ,CAACG,MAAM;QACzCyD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE9D,QAAQ,CAACD,OAAO;QAE3C,IAAIV,aAAa,CAACqB,KAAK,EAAE;UACvBkD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEzE,aAAa,CAACqB,KAAK;QAClD;QACA,IAAIpB,cAAc,CAACoB,KAAK,EAAE;UACxBkD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExE,cAAc,CAACoB,KAAK;QACrD;QACA,IAAInB,aAAa,CAACmB,KAAK,EAAE;UACvBkD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEvE,aAAa,CAACmB,KAAK;QACnD;QAEA,IAAIxB,UAAU,CAACwB,KAAK,EAAE;UACpB,MAAMzC,OAAO,CAAC4D,MAAM,CAAC7B,QAAQ,CAACF,EAAE,EAAE8D,QAAQ;UAC1CpG,SAAS,CAACkE,OAAO,CAAC,MAAM;QAC1B,CAAC,MAAM;UACL,MAAMzD,OAAO,CAAC6D,MAAM,CAAC8B,QAAQ;UAC7BpG,SAAS,CAACkE,OAAO,CAAC,MAAM;QAC1B;QAEA1C,iBAAiB,CAAC0B,KAAK,GAAG,KAAI;QAC9ByB,UAAU,CAAC/C,eAAe,CAACsB,KAAK,CAACZ,EAAE;MACrC,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdxD,SAAS,CAACwD,KAAK,CAAC9B,UAAU,CAACwB,KAAK,GAAG,MAAM,GAAG,MAAM;MACpD,CAAC,SAAS;QACRzB,cAAc,CAACyB,KAAK,GAAG,KAAI;MAC7B;IACF;;IAEA;IACA,MAAMqD,aAAa,GAAGA,CAAA,KAAM;MAC1B/D,QAAQ,CAACF,EAAE,GAAG,EAAC;MACfE,QAAQ,CAACC,WAAW,GAAG,EAAC;MACxBD,QAAQ,CAACE,IAAI,GAAG,EAAC;MACjBF,QAAQ,CAACG,MAAM,GAAG,QAAO;MACzBH,QAAQ,CAACD,OAAO,GAAG,EAAC;;MAEpB;MACAV,aAAa,CAACqB,KAAK,GAAG,IAAG;MACzBpB,cAAc,CAACoB,KAAK,GAAG,IAAG;MAC1BnB,aAAa,CAACmB,KAAK,GAAG,IAAG;MACzBlB,gBAAgB,CAACkB,KAAK,GAAG,EAAC;MAC1BjB,iBAAiB,CAACiB,KAAK,GAAG,EAAC;MAC3BhB,gBAAgB,CAACgB,KAAK,GAAG,EAAC;MAE1B,IAAIvB,WAAW,CAACuB,KAAK,EAAE;QACrBvB,WAAW,CAACuB,KAAK,CAACsB,WAAW,CAAC;MAChC;IACF;;IAEA;IACA,MAAMgC,gBAAgB,GAAGA,CAAA,KAAM;MAC7BnF,KAAK,CAAC6B,KAAK,GAAG,EAAC;MACftB,eAAe,CAACsB,KAAK,GAAG,IAAG;IAC7B;;IAEA;IACA,MAAMuD,UAAU,GAAIC,IAAI,IAAK;MAC3B,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC;IACvC;;IAEA;IACA,MAAMC,YAAY,GAAIC,GAAG,IAAK;MAC5B;MACApB,OAAO,CAACqB,GAAG,CAAC,OAAO,EAAED,GAAG;IAC1B;;IAEA;IACAhH,SAAS,CAAC,MAAM;MACdsD,eAAe,CAAC;IAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}