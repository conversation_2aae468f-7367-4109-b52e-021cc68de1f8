const { pool } = require('./db');

// 初始化数据库表
const initDatabase = async () => {
  try {
    const connection = await pool.getConnection();
    

    
    // 1. 创建分类表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category VARCHAR(100) NOT NULL COMMENT '类别',
        main_category ENUM('头部', '发型', '躯干', '腿部', '配件') NOT NULL COMMENT '大类',
        content TEXT COMMENT '内容',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 2. 创建子元素表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL COMMENT '分类ID',
        name VARCHAR(200) COMMENT '名称',
        thumbnail VARCHAR(255) NOT NULL COMMENT '缩略图',
        front_image VARCHAR(255) NOT NULL COMMENT '前置图',
        back_image VARCHAR(255) NOT NULL COMMENT '后置图',
        status VARCHAR(50) COMMENT '状态',
        content TEXT COMMENT '内容',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 3. 创建订单表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_number VARCHAR(100) NOT NULL UNIQUE COMMENT '订单号',
        name VARCHAR(100) COMMENT '姓名',
        email VARCHAR(100) COMMENT '邮箱',
        phone VARCHAR(20) COMMENT '电话',
        order_items LONGTEXT COMMENT '下单的东西',
        status VARCHAR(50) COMMENT '状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 4. 创建用户表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        email VARCHAR(100) COMMENT '邮箱',
        phone VARCHAR(20) COMMENT '电话',
        role ENUM('admin', 'user') NOT NULL DEFAULT 'user' COMMENT '角色',
        status TINYINT(1) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
        avatar VARCHAR(255) COMMENT '头像',
        last_login TIMESTAMP COMMENT '最后登录时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 插入默认管理员账户和测试用户
    await connection.query(`
      INSERT IGNORE INTO users (id, username, password, email, phone, role, status, created_at) VALUES
      (1, 'admin', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXxVOvJdy.Gn.1eeX6.dJuB4S6', '<EMAIL>', '13800138000', 'admin', 1, '2025-01-01 10:00:00'),
      (2, 'user1', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXxVOvJdy.Gn.1eeX6.dJuB4S6', '<EMAIL>', '13800138001', 'user', 1, '2025-01-01 11:00:00'),
      (3, 'user2', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXxVOvJdy.Gn.1eeX6.dJuB4S6', '<EMAIL>', '13800138002', 'user', 0, '2025-01-01 12:00:00'),
      (4, 'manager', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXxVOvJdy.Gn.1eeX6.dJuB4S6', '<EMAIL>', '13800138003', 'admin', 1, '2025-01-01 13:00:00')
    `);

    // 插入测试订单数据
    await connection.query(`
      INSERT IGNORE INTO orders (id, order_number, name, email, phone, order_items, status, created_at) VALUES
      (1, 'ORD202501010001', '张三', '<EMAIL>', '13800138001', '乐高积木套装 x1, 乐高人仔 x2', 'pending', '2025-01-01 10:00:00'),
      (2, 'ORD202501010002', '李四', '<EMAIL>', '13800138002', '乐高城市系列 x1', 'processing', '2025-01-01 11:30:00'),
      (3, 'ORD202501010003', '王五', '<EMAIL>', '13800138003', '乐高星球大战系列 x1, 乐高配件包 x3', 'completed', '2025-01-01 14:20:00'),
      (4, 'ORD202501010004', '赵六', '<EMAIL>', '13800138004', '乐高建筑系列 x2', 'cancelled', '2025-01-01 16:45:00'),
      (5, 'ORD202501020001', '钱七', '<EMAIL>', '13800138005', '乐高科技系列 x1, 乐高电机 x1', 'pending', '2025-01-02 09:15:00')
    `);

    console.log('数据库表初始化成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    return false;
  }
};

module.exports = {
  initDatabase
}; 
