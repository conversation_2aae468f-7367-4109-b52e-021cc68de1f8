  :root {
        --gradient-start: #6a11cb;
        --gradient-end: #2575fc;
        --card-bg: rgba(255, 255, 255, 0.1);
        --card-border: rgba(255, 255, 255, 0.2);
        --accent-color: #3a7bd5;
        --button-primary: #4776e6;
        --button-danger: #8e2de2;
      }

      body {
        font-family: "Microsoft YaHei", sans-serif;
        background-color: #f8f9fa;
        overflow-x: hidden;
      }

      /* 移动端和PC端特定样式 */
      body.is-mobile .section-card {
        /* border-radius: 10px; */
        margin-bottom: 20px;
      }

      body.is-pc .section-card {
        border-radius: 15px;
        margin-bottom: 30px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      body.is-pc .section-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
      ..bg-body-tertiary {
        background-color: #fff !important;
        font-weight: bold;
        
      }
      /* Tab loading样式 */
      .tab-loading {
        position: absolute;
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
        z-index: 10;
      }

      .tab-button {
        position: relative;
      }

      .tab-loading .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.1em;
        color: #fff;
      }

      /* 工具栏样式 */
      .torso-toolbar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        color: white;
      }

      .toolbar-section {
        margin-bottom: 20px;
      }

      .toolbar-section:last-child {
        margin-bottom: 0;
      }

      /* 背景选择区域样式 */
      .background-section {
        padding: 15px;
      }

      .background-category {
        margin-bottom: 25px;
      }

      .background-category:last-child {
        margin-bottom: 0;
      }

      .background-item {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        border-radius: 8px;
        padding: 5px;
      }

      .background-item:hover {
        border-color: #007bff;
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
      }

      .background-item.selected {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.1);
        transform: scale(1.05);
      }

      .upload-background-item {
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .upload-background-item:hover {
        transform: scale(1.05);
      }

      .upload-background-item:hover div {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.05);
      }

      /* 上传占位符样式 */
      .upload-placeholder {
        width: 80px;
        height: 100px;
        border: 2px dashed #ccc;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
      }

      .upload-placeholder:hover {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.05);
        transform: scale(1.02);
      }

      .upload-placeholder i {
        font-size: 24px;
        color: #666;
        margin-bottom: 5px;
      }

      .upload-placeholder span {
        font-size: 12px;
        color: #666;
        text-align: center;
      }

      /* 背景图片项选中状态 */
      .background-item.selected {
        position: relative;
      }

      .background-item.selected::after {
        content: '✓';
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
      }

      /* 背景选择面板样式 */
      .background-selection-panel {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .background-selection-panel .category-title {
        color: #333;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 16px;
      }

      .background-selection-panel .hair-category {
        margin-bottom: 25px;
      }

      .background-selection-panel .hair-category:last-child {
        margin-bottom: 0;
      }

      .background-selection-panel .hair-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }

      .background-selection-panel .hair-item {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        border-radius: 8px;
        padding: 5px;
        position: relative;
      }

      .background-selection-panel .hair-item:hover {
        border-color: #007bff;
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
      }

      .background-selection-panel .hair-item.selected {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.1);
        transform: scale(1.05);
      }

      .toolbar-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: rgba(255, 255, 255, 0.9);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 8px;
      }

      .toolbar-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
      }

      .toolbar-btn {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        padding: 8px 12px;
        color: white;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        backdrop-filter: blur(10px);
      }

      .toolbar-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      .toolbar-btn:active {
        transform: translateY(0);
      }

      .toolbar-btn i {
        font-size: 14px;
      }

      .text-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 12px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .text-controls input[type="color"] {
        width: 30px;
        height: 30px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        background: none;
      }

      .text-controls input[type="range"] {
        width: 80px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        outline: none;
        cursor: pointer;
      }

      .text-controls input[type="range"]::-webkit-slider-thumb {
        appearance: none;
        width: 16px;
        height: 16px;
        background: white;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .text-controls span {
        font-size: 12px;
        font-weight: 500;
        min-width: 20px;
        text-align: center;
      }

      /* 预设图片面板样式 */
      .preset-image-panel {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e1e1e1;
      }

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #e1e1e1;
        background: #f8f9fa;
        border-radius: 12px 12px 0 0;
      }

      .panel-header h6 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
      }

      .close-btn:hover {
        background: #e9ecef;
        color: #333;
      }

      .preset-images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 15px;
        padding: 20px;
      }

      .preset-image-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }

      .preset-image-item:hover {
        background: #f8f9fa;
        border-color: #667eea;
        transform: translateY(-2px);
      }

      .preset-image-item.selected {
        background: #e3f2fd;
        border-color: #2196f3;
      }

      .preset-image-item img {
        width: 50px;
        height: 50px;
        border-radius: 6px;
        object-fit: cover;
      }

      .preset-image-item span {
        font-size: 11px;
        color: #666;
        text-align: center;
        font-weight: 500;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .torso-toolbar {
          padding: 15px;
          margin-bottom: 15px;
        }

        .toolbar-buttons {
          flex-direction: column;
          align-items: stretch;
        }

        .toolbar-btn {
          justify-content: center;
          padding: 12px;
          font-size: 14px;
        }

        .text-controls {
          flex-direction: column;
          gap: 12px;
          align-items: stretch;
        }

        .text-controls input[type="range"] {
          width: 100%;
        }

        .preset-images-grid {
          grid-template-columns: repeat(3, 1fr);
          gap: 10px;
          padding: 15px;
        }

        .preset-image-item {
          padding: 8px;
        }

        .preset-image-item img {
          width: 40px;
          height: 40px;
        }

        .preset-image-panel {
          max-height: 250px;
        }
      }

      @media (max-width: 480px) {
        .toolbar-title {
          font-size: 13px;
        }

        .toolbar-btn {
          font-size: 13px;
          padding: 10px;
        }

        .toolbar-btn i {
          font-size: 16px;
        }

        .preset-images-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      /* 工具栏动画效果 */
      .torso-toolbar {
        animation: slideInFromTop 0.6s ease-out;
      }

      @keyframes slideInFromTop {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .preset-image-panel {
        animation: slideInFromBottom 0.3s ease-out;
      }

      @keyframes slideInFromBottom {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 工具提示样式 */
      .toolbar-btn[title]:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        white-space: nowrap;
        z-index: 1000;
        margin-bottom: 5px;
      }

      .toolbar-btn {
        position: relative;
      }

      /* 使用提示样式 */
      .usage-tips {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 12px;
        margin-top: 20px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        color: white;
      }

      .tips-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 20px;
        background: rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: background 0.3s ease;
      }

      .tips-header:hover {
        background: rgba(255, 255, 255, 0.15);
      }

      .tips-header i {
        font-size: 16px;
        margin-right: 8px;
      }

      .tips-header span {
        font-weight: 600;
        font-size: 14px;
        flex: 1;
      }

      .toggle-tips-btn {
        background: none;
        border: none;
        color: white;
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s ease;
      }

      .toggle-tips-btn:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .toggle-tips-btn.collapsed i {
        transform: rotate(180deg);
      }

      .tips-content {
        padding: 0 20px 20px;
        max-height: 200px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .tips-content.collapsed {
        max-height: 0;
        padding: 0 20px;
      }

      .tips-content ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .tips-content li {
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        font-size: 13px;
        line-height: 1.4;
      }

      .tips-content li:last-child {
        border-bottom: none;
      }

      .tips-content strong {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
      }

      /* 移动端优化 */
      @media (max-width: 768px) {
        .usage-tips {
          margin-top: 15px;
        }

        .tips-header {
          padding: 12px 15px;
        }

        .tips-content {
          padding: 0 15px 15px;
        }

        .tips-content li {
          font-size: 12px;
          padding: 6px 0;
        }
      }

      .header {
        background-color: #fff;
        padding: 10px;
        border-bottom: 1px solid #e1e1e1;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .header-title {
        color: #d50000;
        font-weight: bold;
        font-size: 14px;
        margin: 0;
      }

      /* 导航菜单样式 */
      .nav-menu {
        display: none;
      }

      .nav-menu.active {
        display: block;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 10px 10px;
      }

      .nav-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .nav-menu li {
        padding: 12px 15px;
        border-bottom: 1px solid #f1f1f1;
      }

      .nav-menu li:last-child {
        border-bottom: none;
      }

      .nav-menu a {
        text-decoration: none;
        color: #333;
        display: block;
        font-size: 14px;
      }

      .menu-button {
        background: none;
        border: none;
        color: #777;
        font-size: 20px;
        cursor: pointer;
      }

      /* PC端菜单样式 */
      @media (min-width: 992px) {
        .menu-button {
          display: none;
        }

        .nav-menu {
          display: block;
          position: static;
          box-shadow: none;
          background-color: transparent;
        }

        .nav-menu ul {
          display: flex;
          align-items: center;
        }

        .nav-menu li {
          border-bottom: none;
          padding: 0 15px;
        }

        .nav-menu a {
          color: #777;
          padding: 5px 0;
        }

        .nav-menu a:hover {
          color: #d50000;
        }
      }

      .main-container {
        margin-top: 70px;
        overflow-x: hidden;
        padding: 0 15px;
      }

      /* 添加通用卡片样式 */
      .section-card {
        background-color: #fff;
        /* border-radius: 15px; */
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-top: 15px;
        margin-bottom: 30px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      /* PC端卡片样式 */
      @media (min-width: 992px) {
        .section-card {
          max-width: 900px;
          margin-left: auto;
          margin-right: auto;
        }

        .main-container {
          padding: 0 30px;
        }
      }

      .section {
        padding: 20px 15px;
        margin-bottom: 20px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      }

      /* 第一板块样式 - 调整后更匹配图片 */
      .welcome-section {
        padding: 0;
        background-color: transparent;
        box-shadow: none;
      }

      .welcome-card {
        background: linear-gradient(to right, #2962ff, #d50000);
        color: white;
        border-radius: 20px;
        padding: 25px 20px;
        margin: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-height: 520px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      /* 在移动设备上调整欢迎卡片的高度 */
      body.is-mobile .welcome-card {
        min-height: 420px;
        padding: 20px 15px;
      }

      /* PC端特定样式 */
      @media (min-width: 992px) {
        .welcome-card {
          min-height: 600px;
        }
      }

      .welcome-title {
        font-size: 1.4rem;
        font-weight: bold;
        margin-bottom: 10px;
        line-height: 1.3;
      }

      .welcome-description {
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 20px;
        opacity: 0.9;
      }

      /* 在PC端上调整字体大小 */
      body.is-pc .welcome-title {
        font-size: 1.8rem;
      }

      body.is-pc .welcome-description {
        font-size: 1rem;
      }

      .welcome-image {
        width: auto;
        max-height: 50px;
        margin-top: 15px;
        display: inline-block;
      }

      /* PC端的图像大小 */
      body.is-pc .welcome-image {
        max-height: 60px;
      }

      .welcome-image-large {
        width: 100%;
        max-width: 350px;
        border-radius: 10px;
        margin-top: 20px;
        display: block;
      }

      .welcome-image-container {
        text-align: center;
        margin-top: 20px;
      }

      .preview-label {
        background-color: white;
        color: var(--accent-color);
        padding: 10px 15px;
        border-radius: 5px;
        margin: 15px 10px 0;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* 调整欢迎卡片在不同设备上的布局 */
      @media (min-width: 768px) {
        .main-container {
          padding: 0;
        }
        .card-body {
          padding: 0;
        }
        .welcome-card {
          padding: 30px;
        }

        .welcome-image-large {
          max-width: 400px;
        }
      }

      .feature-bar {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background-color: white;
        border-radius: 15px;
        margin: 15px 10px 0;
        padding: 15px 0;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #666;
        font-size: 0.8rem;
      }

      .feature-item i {
        font-size: 20px;
        margin-bottom: 5px;
        color: var(--accent-color);
      }

      .lego-figure {
        text-align: center;
        /* padding: 20px; */
        background-color: #ededed;
        border-radius: 10px;
        margin-bottom: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        position: static;
      }

      .lego-figure img {
        max-width: 100%;
        margin: 0 auto;
        /* 移动端图片抗锯齿优化 */
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        -webkit-backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* Canvas元素优化 - 移动端抗锯齿 */
      canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        -webkit-backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .control-buttons {
        display: flex;
        margin: 15px 0;
        gap: 10px;
      }

      .control-buttons .btn {
        flex: 1;
        border-radius: 20px;
      }

      .tab-buttons {
        display: flex;
        justify-content: space-around;
        margin: 15px 0;
        border-bottom: 1px solid #e1e1e1;
        /* padding-bottom: 10px; */
      }

      .tab-button {
        font-size: 12px;
        color: #777;
        text-align: center;
        cursor: pointer;
      }

      .tab-button i {
        display: block;
        font-size: 18px;
        margin-bottom: 5px;
      }

      .tab-button.active {
        color: var(--accent-color);
      }

      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
        animation: fadeIn 0.5s ease;
        flex: 1;
        box-sizing: border-box;
        padding: 15px;
      }

      /* 添加小人切换动画样式 */
      .figure-transition {
        transition: opacity 0.5s ease, transform 0.5s ease;
        opacity: 0;
        transform: scale(0.95);
        display: none;
      }

      .figure-transition.active {
        opacity: 1;
        transform: scale(1);
        display: flex;
      }

      /* 功能提示徽章 */
      .feature-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        animation: pulse 2s infinite;
        z-index: 10;
      }

      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }

      .tab-button {
        position: relative;
      }

      .color-selector {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
        margin: 20px 0;
      }

      .color-option {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        border: 2px solid transparent;
        transition: all 0.3s ease;
      }

      .color-option:hover {
        transform: scale(1.15);
        border-color: var(--accent-color);
      }

      .part-item {
        background-color: white;
        border-radius: 10px;
        padding: 10px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        opacity: 0;
        transform: translateX(-20px);
        animation: slideIn 0.5s ease forwards;
      }

      .part-icon {
        width: 40px;
        height: 40px;
        background-color: #f0f0f0;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .quantity-control {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .btn-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .faq-item {
        margin-bottom: 20px;
        opacity: 0;
        animation: fadeIn 1s ease forwards;
      }

      .faq-question {
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: flex-start;
      }

      .faq-question i {
        color: var(--accent-color);
        margin-right: 10px;
        flex-shrink: 0;
      }

      .faq-answer {
        color: #666;
        font-size: 14px;
        padding-left: 25px;
      }

      .divider {
        height: 20px;
        margin: 30px 0;
        position: relative;
        text-align: center;
      }

      .divider:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #e1e1e1;
      }

      .divider span {
        background-color: #f8f9fa;
        padding: 0 15px;
        position: relative;
        color: #999;
        font-size: 12px;
      }

      /* 卡片头部标题栏 */
      .card-header {
        padding: 15px 20px;
        background-color: #3a7bd5;
        color: white;
        border: none;
      }

      /* 特定卡片头部样式 */
      .preview-header {
        background-color: #3a7bd5;
      }

      .customize-header {
        background-color: #d50000;
      }

      .faq-header {
        background-color: #3a7bd5;
      }

      /* 卡片内容区 */
      .card-body {
        padding: 20px;
      }

      /* 动画效果 */
      @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateX(-20px);
        }

        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes bounceIn {
        0% {
          transform: scale(0.3);
          opacity: 0;
        }

        50% {
          transform: scale(1.05);
        }

        70% {
          transform: scale(0.9);
        }

        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      .color-option.animate {
        animation: bounceIn 0.6s ease-in-out;
      }

      /* 按钮样式强化 */
      .btn {
        border-radius: 30px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
      }

      .btn-circle {
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background: white;
        color: var(--accent-color);
        border: 1px solid #e4e4e4;
      }

      .btn-circle:hover {
        background-color: var(--accent-color);
        color: white;
      }

      /* 部件项美化 */
      .part-item {
        border-radius: 12px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
      }

      .part-item:hover {
        background-color: #f9f9f9;
        border-left-color: var(--accent-color);
      }

      .part-icon {
        width: 40px;
        height: 40px;
        background-color: #f0f8ff;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--accent-color);
      }

      /* 设置不同部件项的动画延迟 */
      .part-item:nth-child(1) {
        animation-delay: 0.1s;
      }

      .part-item:nth-child(2) {
        animation-delay: 0.2s;
      }

      .part-item:nth-child(3) {
        animation-delay: 0.3s;
      }

      .part-item:nth-child(4) {
        animation-delay: 0.4s;
      }

      .part-item:nth-child(5) {
        animation-delay: 0.5s;
      }

      /* 设置不同FAQ项的动画延迟 */
      .faq-item:nth-child(1) {
        animation-delay: 0.1s;
      }

      .faq-item:nth-child(2) {
        animation-delay: 0.3s;
      }

      .faq-item:nth-child(3) {
        animation-delay: 0.5s;
      }

      .faq-item:nth-child(4) {
        animation-delay: 0.7s;
      }

      .scroll-reveal {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease, transform 0.6s ease;
      }

      .scroll-reveal.visible {
        opacity: 1;
        transform: translateY(0);
      }

      /* PC端特定样式 */
      @media (min-width: 992px) {
        .welcome-card {
          min-height: 350px;
        }

        .lego-figure img {
          max-width: 309px;
        }

        .control-buttons {
          max-width: 500px;
          margin-left: auto;
          margin-right: auto;
        }

        .color-selector {
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }
      }

      /* 按钮样式调整 */
      .btn-danger {
        background: #d50000;
        border: none;
      }

      .btn-outline-primary {
        color: var(--accent-color);
        border-color: var(--accent-color);
      }

      .btn-outline-primary:hover {
        background-color: var(--accent-color);
        color: white;
      }

      /* 弹窗样式 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6); /* 增加遮罩透明度 */
        z-index: 10000; /* 提高z-index */
        display: none;
        justify-content: center;
        align-items: center;
      }

      /* 阻止页面滚动 */
      body.modal-open {
        overflow: hidden;
      }

      .modal-content {
        background: white;
        border-radius: 15px;
        width: 100%;
        height: 100%;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        overflow: hidden;
      }

      .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .modal-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0;
      }

      .modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.3s ease;
      }

      .modal-close:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .modal-body {
        padding: 20px;
        height: calc(100% - 80px);
        overflow-y: auto;
      }

      /* 移动端隐藏弹窗，使用原有切换逻辑 */
      @media (max-width: 991px) {
        .modal-overlay {
          display: none !important;
        }
      }

      /* 躯干选择样式 */
      .torso-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .torso-item {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
      }

      .torso-item:hover {
        border-color: #667eea;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
      }

      .torso-item.selected {
        border-color: #667eea;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.1) 0%,
          rgba(118, 75, 162, 0.1) 100%
        );
      }

      .torso-item img {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin-bottom: 10px;
      }

      .torso-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .torso-preview {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .preview-figure img {
        max-width: 150px;
        max-height: 150px;
        object-fit: contain;
      }

      .modal-actions {
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #e0e0e0;
      }

      /* 发型选择样式 */
      .style-section {
        /* margin: 20px 0; */
      }

      .category {
        margin-bottom: 20px;
      }

      .category-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
      }

      .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 15px;
        justify-items: center;
        
      }

      .hair-item, .head-item, .expression-item, .details-item, .clothing-item, .decoration-item {
        border: 1px solid #e0e0e0;
        padding: 5px;
        cursor: pointer;
        position: relative;
        background-color: white;
        transition: all 0.2s;
        width: 100%;
        max-width: 100px;
        aspect-ratio: 1 / 1;
        overflow: hidden;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hair-item:hover, .head-item:hover, .expression-item:hover, .details-item:hover, .clothing-item:hover, .decoration-item:hover {
        border-color: #ccc;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .hair-item.selected, .head-item.selected, .expression-item.selected, .details-item.selected, .clothing-item.selected, .decoration-item.selected {
        border: 2px solid #ffd700;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
      }

      .hair-item img, .head-item img, .expression-item img, .details-item img, .clothing-item img, .decoration-item img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block;
      }

      .hair-check, .head-check, .expression-check, .details-check, .clothing-check, .decoration-check {
        position: absolute;
        bottom: 5px;
        right: 5px;
        background-color: #4caf50;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      /* 3D人仔预览样式 */
      .lego-figure {
        position: relative;
        margin: 0 auto;
        /* width: 100%; */
        width: 100%;
        height: 100%;
        /* height: 400px; */
        position: relative;
        /* box-sizing: border-box; */
        margin: 0 10%;
        aspect-ratio: 1 / 1;
      }

      .minifigure-part {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .minifigure-head {
        z-index: 2;
      }

      .minifigure-hair {
        z-index: 3;
      }

      .minifigure-torso {
        z-index: 1;
      }

      .minifigure-legs {
        z-index: 0;
      }

      @media (min-width: 36.25em) {
        .lego-figure-ca {
          width: 100%;
          max-width: 386px;
          aspect-ratio: unset;
        }
      }

      .lego-figure-ca {
        pointer-events: none;
        position: absolute;
        top: 0;
        height: 100%;
        max-width: 100%;
        aspect-ratio: 8/9;
        left: 50%;
        transform: translate(-50%);
      }

      .lego-figure-ca2 {
        position: relative;
        transform-origin: center center;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .lego-figure-ca4 {
        position: absolute;
      }

      .MinifigureBrick_overlay__zBY1_.MinifigureBrick_cropped__9s96E {
    opacity: 1;
    position: absolute;
    aspect-ratio: 160 / 130;
    width: 30%;
    left: 50%;
    bottom: 50%;
    transform: translate(-50%, 62%);
}

      .main-figure-flex {
        /* display: flex; */
        flex-direction: row;
        gap: 32px;
      }

      .sidebar {
        flex: 1;
        display: flex;
        flex-direction: column;
        /* gap: 24px; */
      }

      .preview {
        width: 309px;
        display: flex;
        height: 442px;
        align-items: center;
        justify-content: center;
      }

      @media (max-width: 768px) {
        .tab-content {
          height: 400px;
          overflow: auto;
          flex: auto !important;
        }
        
        .category-grid img {
          height: 70px !important;
          width: 70px !important;
        }
         .main-container {
          padding: 0;
        }
        .card-body {
          padding: 0;
        }
        .main-figure-flex {
          flex-direction: column;
        }

        .sidebar {
          flex: 1;
          order: 2;
        }

        .preview {
          width: 100%;
          height: 442px;
          order: 1;
          margin-bottom: 16px;
        }
      }

      /* 图片选择面板样式 */
      .image-selection-panel {
        padding: 15px;
        background: white;
        border-radius: 8px;
        margin-top: 10px;
      }

      .image-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        margin: 10px 0;
      }

      .image-item {
        border: 2px solid #ddd;
        border-radius: 8px;
        padding: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .image-item:hover {
        border-color: #0066ff;
        transform: scale(1.05);
      }

      .image-item.selected {
        border-color: #0066ff;
        background-color: #f0f8ff;
      }

      .image-item img {
        width: 100%;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
      }

      /* 编辑区域限制样式 */
      .edit-area-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10;
      }

      .edit-area-boundary {
        position: absolute;
        border: 2px dashed #0066ff;
        background: rgba(0, 102, 255, 0.1);
        pointer-events: none;
      }

      .konvajs-content {
        width: 100%;
        aspect-ratio: ;
      }
       .modal-content .modal-content-padding {
            padding: 1.5em;
            letter-spacing: 0;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400;
        }
        .CustomiseTorso_konvaWrapper__w_9lQ {
    height: 100%;
    display: flex
;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}
.CustomiseTorso_bottomButtonsWrapper__x46t1 {
    display: flex
;
    align-items: center;
    margin-top: auto;
    padding: 0 12px;
    width: 100%;
    gap: 10px;
}
.CustomiseTorso_topButtonsWrapper__UMfqv
 {
    padding: 12px;
    z-index: 1;
    grid-row: 1;
    display: flex
;
    gap: 10px;
    justify-content: end;
}
.CustomiseTorso_konvaWrapper__w_9lQ>div:first-child {
    aspect-ratio: 1 / 1;
    width: 100%;
    margin: auto 0;
    overflow: hidden;
    display: flex;
    align-items: center;
}
.CustomiseTorso_konvaWrapper__w_9lQ>div:first-child>div:first-child {
    scale: 1.4;
}


        @media (min-width: 36.25em) {
    .modal-content .modal-container>div {
        max-height: 600px;
        border-radius: 0;
        height: calc(100dvh - 4rem / 2);
        overflow: hidden;
        display: grid
;
        grid-template-columns: minmax(min-content, 842px);
    }
        .customer-container {
        max-height: 600px;
        grid-template-columns: minmax(min-content, 842px);
        grid-template-rows: auto;
    width: 100%;
    display: grid
;
            touch-action: none;
    }
    .customer-container-wrapper {
        position: absolute;
        
        top: 1.5rem;
        left: 1.5rem;
        left: 1.5rem;
        max-width: 420px;
        display: grid;
        grid-template-rows: min-content 1fr;
        grid-row: unset;
        grid-gap: 12px;
        gap: 12px;
        justify-content: normal;
        width: 48%;
        height: calc(100% - (2 * 1.5rem));
        padding: 20px;
        overflow: hidden;
        z-index: 1;
    }
    .customer-container-wrapper2 {
        background: #ededed;
        max-height: 600px;
    display: grid
;
    grid-template-rows: min-content min-content 1fr;
    }
    .CustomiseTorso_topButtonsWrapper__UMfqv {
        position: absolute;
        right: 1.5rem;
        right: var(--ds-spacing-md);
        padding: 20px;
    }
    .CustomiseTorso_konvaWrapper__w_9lQ {
        transform: translateX(22%);
    }
    .CustomiseTorso_bottomButtonsWrapper__x46t1 {
        position: absolute;
        bottom: 1.5rem;
        bottom: var(--ds-spacing-md);
        right: 1.5rem;
        padding: 20px;
        margin: initial;
        width: 50%;
    }
    .CustomiseTorso_konvaWrapper__w_9lQ>div:first-child {
        width: auto;
        margin: initial;
    }
    .CustomiseTorso_konvaWrapper__w_9lQ>div:first-child>div:first-child {
        scale: 1;
    }
}
@media (min-width: 23.4375em) {
    .CustomiseTorso_konvaWrapper__w_9lQ>div:first-child {
        aspect-ratio: 9 / 8;
    }
}

  .lego-figure-ca4 {
        transition: transform 0.2s ease;
      }

      .lego-figure-ca4.switching {
        transform: scale(1.05);
      }

      .control-buttons {
        position: absolute;
        z-index: 100;
        top: 10px;
        left: 10px;
      }

      .btn {
        border: none;
        cursor: pointer;
        margin: 2px;
        transition: all 0.2s ease;
      }

      .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.2rem;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .btn-primary {
        background-color: #007bff;
        color: white;
      }

      .btn-success {
        background-color: #28a745;
        color: white;
      }

      .btn-info {
        background-color: #17a2b8;
        color: white;
      }

      .btn:hover {
        opacity: 0.8;
        transform: scale(1.05);
      }

      /* 移动端布局样式 */
      @media (max-width: 768px) {
        .customer-container {
          width: 100% !important;
          max-width: 100% !important;
          height: 884px !important;
          max-height: 884px !important;
          display: flex !important;
          flex-direction: column !important;
          grid-template-columns: none !important;
          grid-template-rows: none !important;
          overflow: hidden !important;
        }
        .CustomiseTorso_konvaWrapper__w_9lQ>div:first-child {
          aspect-ratio: 1;
          margin: 0;
          overflow: visible;
          height: 100%;
        }

        .customer-container-wrapper2 {
          width: 100%;
          /* max-width: 442px !important; */
          height: 442px !important;
          max-height: 442px !important;
          order: 1 !important; /* 放在上面 */
          margin: 0 auto !important;
          position: relative !important;
          flex-shrink: 0 !important;
        }

        .customer-container-wrapper {
          width: 442px !important;
          max-width: 442px !important;
          height: 442px !important;
          max-height: 442px !important;
          order: 2 !important; /* 放在下面 */
          margin: 0 auto !important;
          position: relative !important;
          top: auto !important;
          left: auto !important;
          padding: 20px !important;
          flex-shrink: 0 !important;
          overflow-y: auto !important;
        }
        .modal-content .modal-content-padding {
          padding: 0;
        }
        /* 确保Canvas容器也适应移动端布局 */
        .CustomiseTorso_konvaWrapper__w_9lQ {
          transform: none !important;
          width: 100% !important;
          height: 100% !important;
          display: flex !important;
          flex-direction: row;
          justify-content: center !important;
          align-items: center !important;
        }

        .konvajs-content {
          max-width: 400px !important;
          max-height: 400px !important;
          margin: 0 auto !important;
        }

        /* 工具栏在移动端的优化 */
        .torso-toolbar {
          padding: 15px !important;
          margin-bottom: 15px !important;
        }

        .toolbar-section {
          margin-bottom: 15px !important;
        }

        .toolbar-buttons {
          flex-wrap: wrap !important;
          gap: 8px !important;
        }

        .toolbar-btn {
          font-size: 12px !important;
          padding: 6px 10px !important;
        }

        .toolbar-btn span {
          display: none !important;
        }

        .toolbar-btn i {
          font-size: 16px !important;
        }
      }
/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 16px;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state p {
  margin: 0;
  font-weight: 500;
}

.empty-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #ccc;
  font-size: 14px;
}

.empty-category i {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-category span {
  font-weight: 400;
}

/* 躯干操作按钮样式 */
.torso-action-buttons {
  position: absolute;
    bottom: 15px;
    right: 50%;
    z-index: 10;
    display: flex
;
    gap: 10px;
    transform: translate(50%, 0);
}

.torso-action-buttons .back-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.torso-action-buttons .btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.torso-action-buttons .btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.torso-action-buttons .btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}
.torso-tab-nav {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 15px;
}

.torso-tab-btn {
  flex: 1;
  padding: 10px 8px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-size: 12px;
}

.torso-tab-btn:hover {
  background-color: #f5f5f5;
}

.torso-tab-btn.active {
  border-bottom-color: #007bff;
  color: #007bff;
  font-weight: 500;
}

.torso-tab-btn i {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

.torso-tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel h6 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.clothing-grid, .decoration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
  margin-bottom: 15px;
}

.clothing-item, .decoration-item {
  padding: 8px;
  border: 2px solid #ddd;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.clothing-item:hover, .decoration-item:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.clothing-item.selected {
  border-color: #28a745;
  background-color: #f8fff9;
}

.clothing-item img, .decoration-item img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  margin-bottom: 4px;
  border-radius: 4px;
}

.clothing-item span, .decoration-item span {
  display: block;
  font-size: 10px;
  color: #666;
}

.text-controls {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.input-group {
  display: flex;
  margin-bottom: 10px;
}

.input-group input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 12px;
}

.input-group button {
  padding: 6px 12px;
  border: 1px solid #007bff;
  border-left: none;
  border-radius: 0 4px 4px 0;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.input-group button:hover {
  background: #0056b3;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.control-row label {
  min-width: 40px;
  font-weight: 500;
}

.control-row input[type="color"] {
  width: 30px;
  height: 25px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.control-row select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 11px;
  min-width: 80px;
}

.control-row input[type="range"] {
  flex: 1;
  margin: 0 8px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .clothing-grid, .decoration-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .control-row {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .control-row label {
    min-width: 35px;
  }
}

/* PC端文字工具布局优化 */
@media (min-width: 992px) {
  /* 文字工具区域 */
  .text-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
    backdrop-filter: blur(10px);
  }

  /* 输入框组 */
  .text-controls .input-group {
    display: flex;
    gap: 8px;
    width: 100%;
  }

  .text-controls .input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
  }

  .text-controls .input-group button {
    padding: 8px 12px;
    border-radius: 6px;
    border: none;
    background: var(--accent-color);
    color: white;
    cursor: pointer;
  }

  /* 控制行 - 每行一个控件 */
  .text-controls .control-row {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
  }

  .text-controls .control-row label {
    color: #495057;
    font-weight: 600;
    font-size: 13px;
    min-width: 50px;
    margin: 0;
  }

  /* 颜色选择器行 */
  .text-controls .control-row:nth-child(2) input[type="color"] {
    width: 40px;
    height: 35px;
    border-radius: 6px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    flex: 1;
  }

  /* 字体选择器行 */
  .text-controls .control-row:nth-child(2) select {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    background: white;
  }

  /* 大小滑块行 */
  .text-controls .control-row:nth-child(3) input[type="range"] {
    flex: 1;
    height: 6px;
    background: linear-gradient(to right, #e9ecef, var(--accent-color));
    border-radius: 3px;
    outline: none;
    margin: 0 10px;
  }

  .text-controls .control-row:nth-child(3) input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    background: var(--accent-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(58, 123, 213, 0.3);
  }

  .text-controls .control-row:nth-child(3) span {
    background: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    min-width: 35px;
    text-align: center;
  }

  /* 删除按钮行 */
  .text-controls .control-row:nth-child(4) {
    justify-content: flex-start;
  }

  .text-controls .control-row:nth-child(4) button {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    background: #dc3545;
    color: white;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .text-controls .control-row:nth-child(4) button:hover {
    background: #c82333;
    transform: translateY(-1px);
  }

  /* 如果需要分开颜色和字体到不同行 */
  .text-controls .control-row.color-row {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .text-controls .control-row.font-row {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.empty-parts {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.empty-parts i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.part-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.part-item:hover {
  background: #e9ecef;
}

.part-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.part-item .bi-x:hover {
  color: #dc3545;
  transform: scale(1.1);
}


